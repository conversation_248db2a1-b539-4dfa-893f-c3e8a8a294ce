import 'package:fpdart/fpdart.dart';
import 'package:leadrat/core_main/common/base/usecase/use_case.dart';
import 'package:leadrat/core_main/errors/failure.dart';
import 'package:leadrat/features/properties/data/models/gallery_dropdown_model.dart';
import 'package:leadrat/features/properties/domain/repository/properties_repository.dart';

class GetGalleryDropdownUseCase implements UseCase<GalleryDropdownModel, NoParams> {
  final PropertiesRepository repository;

  GetGalleryDropdownUseCase({required this.repository});

  @override
  Future<Either<Failure, GalleryDropdownModel>> call(NoParams params) async {
    return await repository.getGalleryDropdownData();
  }
}
