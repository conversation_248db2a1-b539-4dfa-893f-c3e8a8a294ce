import 'package:fpdart/fpdart.dart';
import 'package:leadrat/core_main/common/data/user/models/user_details_model.dart';
import 'package:leadrat/core_main/enums/property_enums/property_status.dart';
import 'package:leadrat/core_main/errors/failure.dart';
import 'package:leadrat/core_main/remote/rest_response/paged_rest_response.dart';
import 'package:leadrat/core_main/utilities/type_def.dart';
import 'package:leadrat/features/lead/domain/entities/get_lead_entity.dart';
import 'package:leadrat/features/properties/data/models/gallery_dropdown_model.dart';
import 'package:leadrat/features/properties/data/models/list_property_model.dart';
import 'package:leadrat/features/properties/data/models/properties_with_id_model.dart';
import 'package:leadrat/features/properties/data/models/property_filter_model.dart';
import 'package:leadrat/features/properties/data/models/update_property_assignedTo_model.dart';
import 'package:leadrat/features/properties/data/models/update_property_share_count_model.dart';
import 'package:leadrat/features/properties/domain/entities/get_all_property_entity.dart';
import 'package:leadrat/features/properties/domain/entities/get_property_entity.dart';
import 'package:leadrat/features/properties/domain/entities/property_lead_count_entity.dart';

import '../../data/models/add_property_model.dart';
import '../../data/models/re_assign_property_model.dart';

abstract class PropertiesRepository {
  FutureEitherFailure<PagedResponse<GetAllPropertyEntity, String>?> getAllProperties(int pageNumber, PropertyFilterModel? propertyFilterModel);

  FutureEitherFailure<PagedResponse<GetAllPropertyEntity, String>?> searchProperties(String searchText, int pageNumber, {bool isPropertyListingEnabled = false});

  FutureEitherFailure<bool?> updatePropertyShareCount(UpdatePropertyShareCountModel updatePropertyShareCountModel);

  FutureEitherFailure<GetPropertyEntity?> getPropertyById(String propertyId);

  FutureEitherFailure<PagedResponse<LeadWithDegreeMatchedEntity, String>?> getMatchingLeads({int? pageNumber, int? pageSize, required String propertyId, bool? isPropertyListingEnabled});

  FutureEitherFailure<List<String>?> getOwnerNames();

  FutureEitherFailure<List<String>?> getAddresses();

  FutureEitherFailure<List<PropertyLeadCountEntity?>?> getMatchingAssociateLeadsCount(String propertyId);

  FutureEitherFailure<List<PropertiesWithIdModel>?> getPropertyNameWithId({bool restore = false, Duration duration = const Duration(seconds: 60)});

  Future<bool?> reAssignProperty(ReAssignPropertyModel? reAssignModel);

  Future<bool?> updatePropertyAssignedTo(UpdatePropertyAssignedToModel? updatePropertyAssignedToModel);

  Future<AddPropertyModel?> addProperty(AddPropertyModel? addPropertyModel);

  Future<AddPropertyModel?> updateProperty(AddPropertyModel? updatePropertyModel);

  Future<String?> deleteProperty(String? propertyId);

  FutureEitherFailure<bool?> archiveProperty(List<String?> archivePropertyList);

  FutureEitherFailure<Map<PropertyVisibility, int>?> getPropertyListingCount({PropertyFilterModel? propertyFilterModel});

  FutureEitherFailure<PagedResponse<GetAllPropertyEntity, String>?> getAllPropertyListing(int pageNumber, PropertyFilterModel? propertyFilterModel, [int pageSize = 10]);

  FutureEitherFailure<bool?> listProperties(ListPropertyModel models);

  FutureEitherFailure<bool?> deListProperties(ListPropertyModel models);

  Future<List<String>?> getCurrencies();

  FutureEitherFailure<String> getTemplateMessageByProperty({String? header, String? message, String? footer, UserDetailsModel? userDetails, String? domain, List<GetPropertyEntity?>? propertyEntity, String? leadName});

  Future<Either<Failure, GalleryDropdownModel>> getGalleryDropdownData();
}
