part of 'injection_container.dart';

final getIt = GetIt.instance;

Future<void> initDependencies() async {
  try {
    getIt.registerLazySingleton<LocalStorageService>(() => HiveServiceImpl());
    await getIt<LocalStorageService>().initialize();

    _registerDataSources();
    _registerRepositories();
    _registerUseCase();
    _registerBlocs();
    _registerServices();
    _registerManagers();
    _registerMappers();
  } catch (e, stackTrace) {
    e.logException(stackTrace);
  }
}

registerBlocProviders(BuildContext context) {
  return [
    BlocProvider(create: (context) => getIt<UserProfileBloc>()),
    BlocProvider(create: (context) => getIt<EditBasicInfoBloc>()),
    BlocProvider(create: (context) => getIt<UserDocumentBloc>()),
    BlocProvider(create: (context) => getIt<ChangePasswordBloc>()),
    BlocProvider(create: (context) => getIt<AddExperienceDocumentBloc>()),
    BlocProvider(create: (context) => getIt<SettingsBloc>()),
    BlocProvider(create: (context) => getIt<SettingSyncBloc>()),
    BlocProvider(create: (context) => getIt<ConnectDomainBloc>()),
    BlocProvider(create: (context) => getIt<AuthDomainBloc>()),
    BlocProvider(create: (context) => getIt<AuthLoginBloc>()),
    BlocProvider(create: (context) => getIt<ManagePropertyBloc>()),
    BlocProvider(create: (context) => getIt<LeadratHomeBloc>()),
    BlocProvider(create: (context) => getIt<ManageLeadsBloc>()),
    BlocProvider(create: (context) => getIt<BasicUserDetailsCubit>()),
    BlocProvider(create: (context) => getIt<ManageDataBloc>()),
    BlocProvider(create: (context) => getIt<DataDetailsBloc>()),
    BlocProvider(create: (context) => getIt<AuthForgetPasswordBloc>()),
    BlocProvider(create: (context) => getIt<RecoverAccountBloc>()),
    BlocProvider(create: (context) => getIt<VerifyOtpBloc>()),
    BlocProvider(create: (context) => getIt<ResetPasswordBloc>()),
    BlocProvider(create: (context) => getIt<AttendanceBloc>()),
    BlocProvider(create: (context) => getIt<AttendanceHistoryBloc>()),
    BlocProvider(create: (context) => getIt<LeadInfoBloc>()),
    BlocProvider(create: (context) => getIt<LeadHistoryBloc>()),
    BlocProvider(create: (context) => getIt<LeadDocumentsBloc>()),
    BlocProvider(create: (context) => getIt<DataNotesBloc>()),
    BlocProvider(create: (context) => getIt<PropertyInfoBloc>()),
    BlocProvider(create: (context) => getIt<PropertyFilterBloc>()),
    BlocProvider(create: (context) => getIt<DataHistoryBloc>()),
    BlocProvider(create: (context) => getIt<LeadFilterBloc>()),
    BlocProvider(create: (context) => getIt<LeadNotesBloc>()),
    BlocProvider(create: (context) => getIt<MessageTemplateBloc>()),
    BlocProvider(create: (context) => getIt<ConvertToLeadBloc>()),
    BlocProvider(create: (context) => getIt<UpdateDataStatusBloc>()),
    BlocProvider(create: (context) => getIt<AppointmentsBloc>()),
    BlocProvider(create: (context) => getIt<SearchLocationBloc>()),
    BlocProvider(create: (context) => getIt<UpdateLeadStatusBloc>()),
    BlocProvider(create: (context) => getIt<DataSearchBloc>()),
    BlocProvider(create: (context) => getIt<DataFilterBloc>()),
    BlocProvider(create: (context) => getIt<PropertyShareBloc>()),
    BlocProvider(create: (context) => getIt<AddLeadBloc>()),
    BlocProvider(create: (context) => getIt<SearchBloc>()),
    BlocProvider(create: (context) => getIt<MatchingLeadsBloc>()),
    BlocProvider(create: (context) => getIt<AddDataBloc>()),
    BlocProvider(create: (context) => getIt<MatchingPropertiesBloc>()),
    BlocProvider(create: (context) => getIt<QrCodeBloc>()),
    BlocProvider(create: (context) => getIt<NotificationBloc>()),
    BlocProvider(create: (context) => getIt<BasicInfoBloc>()),
    BlocProvider(create: (context) => getIt<PropertyInfoTabBloc>()),
    BlocProvider(create: (context) => getIt<FacilitiesBloc>()),
    BlocProvider(create: (context) => getIt<GalleryTabBloc>()),
    BlocProvider(create: (context) => getIt<SplashCubit>()),
    BlocProvider(create: (context) => getIt<BookingFormBloc>()),
    BlocProvider(create: (context) => getIt<MatchingProjectsBloc>()),
    BlocProvider(create: (context) => getIt<MultiFactorAuthenticationBloc>()),
    BlocProvider(create: (context) => getIt<WhatsappChatBloc>()),
    BlocProvider(create: (context) => getIt<CrmTemplatesBloc>()),
    BlocProvider(create: (context) => getIt<WhatsappMediaPreviewBloc>()),
    BlocProvider(create: (context) => getIt<CustomAddLeadBloc>()),
    BlocProvider(create: (context) => getIt<ProjectInfoBloc>()),
    BlocProvider(create: (context) => getIt<ProjectFilterBloc>()),
    BlocProvider(create: (context) => getIt<MatchingLeadsByProjectBloc>()),
    BlocProvider(create: (context) => getIt<ManageProjectsBloc>()),
    BlocProvider(create: (context) => getIt<DashBoardBloc>()),
    BlocProvider(create: (context) => getIt<ListingManagementBloc>()),
    BlocProvider(create: (context) => getIt<ListingManagementFilterBloc>()),
    BlocProvider(create: (context) => getIt<CustomAddDataBloc>()),
    BlocProvider(create: (context) => getIt<CustomDashboardBloc>()),
    BlocProvider(create: (context) => getIt<DashboardFilterBloc>()),
    BlocProvider(create: (context) => getIt<SavedFilterBloc>()),
    BlocProvider(create: (context) => getIt<SaveFilterCubit>()),
    BlocProvider(create: (context) => getIt<PublishingTabBloc>()),
    BlocProvider(create: (context) => getIt<AddPropertyListingBloc>()),
    BlocProvider(create: (context) => getIt<PropertyListingBasicInfoBloc>()),
    BlocProvider(create: (context) => getIt<PropertyListingPropertyInfoBloc>()),
    BlocProvider(create: (context) => getIt<PropertyListingFacilitiesBloc>()),
    BlocProvider(create: (context) => getIt<PropertyListingGalleryBloc>()),
    BlocProvider(create: (context) => getIt<PropertyListingTabBloc>()),
    BlocProvider(create: (context) => getIt<PropertyListingLocationBloc>()),
    BlocProvider(create: (context) => getIt<BrevoEmailTemplateBloc>()),
    BlocProvider(create: (context) => getIt<ManageTaskBloc>()),
    BlocProvider(create: (context) => getIt<AddTaskBloc>()),
    BlocProvider(create: (context) => getIt<TaskDetailsBloc>()),
  ];
}

void _registerBlocs() {
  getIt
    ..registerLazySingleton(() => UserProfileBloc(getIt(), getIt(), getIt(), getIt()))
    ..registerLazySingleton(() => EditBasicInfoBloc(getIt()))
    ..registerLazySingleton(() => UserDocumentBloc(getIt(), getIt(), getIt()))
    ..registerLazySingleton(() => ChangePasswordBloc(getIt()))
    ..registerLazySingleton(() => AddExperienceDocumentBloc(getIt(), getIt()))
    ..registerLazySingleton(() => ConnectDomainBloc(getIt()))
    ..registerLazySingleton(() => AuthDomainBloc(getIt()))
    ..registerLazySingleton(() => AuthLoginBloc(getIt(), getIt(), getIt()))
    ..registerLazySingleton(() => ManagePropertyBloc(getIt(), getIt(), getIt(), getIt(), getIt(), getIt(), getIt()))
    ..registerLazySingleton(() => ListingManagementBloc(getIt(), getIt(), getIt(), getIt(), getIt(), getIt(), getIt(), getIt(), getIt(), getIt()))
    ..registerLazySingleton(() => ListingManagementFilterBloc(getIt(), getIt(), getIt(), getIt(), getIt(), getIt(), getIt()))
    ..registerLazySingleton(() => SettingSyncBloc(getIt()))
    ..registerLazySingleton(() => SettingsBloc(getIt(), getIt(), getIt(), getIt()))
    ..registerLazySingleton(() => LeadratHomeBloc(getIt(), getIt(), getIt(), getIt()))
    ..registerLazySingleton(() => BasicUserDetailsCubit(getIt()))
    ..registerLazySingleton(() => ManageLeadsBloc(getIt(), getIt(), getIt(), getIt(), getIt(), getIt()))
    ..registerLazySingleton(() => LeadInfoBloc(getIt(), getIt(), getIt(), getIt(), getIt(), getIt(), getIt(), getIt(), getIt(), getIt(), getIt(), getIt()))
    ..registerLazySingleton(() => LeadHistoryBloc(getIt(), getIt(), getIt()))
    ..registerLazySingleton(() => AuthForgetPasswordBloc(getIt(), getIt()))
    ..registerLazySingleton(() => RecoverAccountBloc(getIt()))
    ..registerLazySingleton(() => VerifyOtpBloc(getIt(), getIt()))
    ..registerLazySingleton(() => ResetPasswordBloc(getIt()))
    ..registerLazySingleton(() => QrCodeBloc(getIt(), getIt()))
    ..registerLazySingleton(() => PropertyInfoBloc(getIt(), getIt(), getIt(), getIt(), getIt(), getIt(), getIt()))
    ..registerLazySingleton(() => PropertyFilterBloc(getIt(), getIt(), getIt(), getIt(), getIt(), getIt()))
    ..registerLazySingleton(() => ManageDataBloc(getIt(), getIt(), getIt(), getIt(), getIt(), getIt()))
    ..registerLazySingleton(() => AttendanceBloc(getIt(), getIt(), getIt(), getIt(), getIt(), getIt(), getIt()))
    ..registerLazySingleton(() => AttendanceHistoryBloc(getIt()))
    ..registerLazySingleton(() => LeadDocumentsBloc(getIt(), getIt(), getIt()))
    ..registerLazySingleton(() => DataDetailsBloc(getIt(), getIt(), getIt(), getIt(), getIt(), getIt(), getIt(), getIt()))
    ..registerLazySingleton(() => DataNotesBloc(getIt(), getIt()))
    ..registerLazySingleton(() => DataHistoryBloc(getIt(), getIt(), getIt()))
    ..registerLazySingleton(() => LeadFilterBloc(getIt(), getIt(), getIt(), getIt(), getIt(), getIt(), getIt(), getIt(), getIt(), getIt(), getIt(), getIt(), getIt()))
    ..registerLazySingleton(() => LeadNotesBloc(getIt(), getIt(), getIt()))
    ..registerLazySingleton(() => MessageTemplateBloc(getIt(), getIt(), getIt(), getIt(), getIt(), getIt()))
    ..registerLazySingleton(() => ConvertToLeadBloc(getIt(), getIt(), getIt(), getIt(), getIt(), getIt()))
    ..registerLazySingleton(() => UpdateDataStatusBloc(getIt(), getIt()))
    ..registerLazySingleton(() => AddLeadBloc(getIt(), getIt(), getIt(), getIt(), getIt(), getIt(), getIt(), getIt(), getIt(), getIt(), getIt(), getIt(), getIt()))
    ..registerFactory(() => ReadMoreCubit())
    ..registerFactory(() => ImagePreviewCubit())
    ..registerLazySingleton(() => UpdateLeadStatusBloc(getIt(), getIt(), getIt(), getIt(), getIt(), getIt(), getIt(), getIt(), getIt(), getIt()))
    ..registerLazySingleton(() => AppointmentsBloc(getIt(), getIt(), getIt(), getIt(), getIt(), getIt(), getIt()))
    ..registerLazySingleton(() => SearchLocationBloc(getIt(), getIt()))
    ..registerLazySingleton(() => PropertyShareBloc(getIt(), getIt(), getIt(), getIt(), getIt(), getIt()))
    ..registerLazySingleton(() => DataSearchBloc(getIt(), getIt(), getIt()))
    ..registerLazySingleton(() => DataFilterBloc(getIt(), getIt(), getIt(), getIt(), getIt(), getIt(), getIt(), getIt(), getIt(), getIt(), getIt(), getIt(), getIt()))
    ..registerLazySingleton(() => AddDataBloc(getIt(), getIt(), getIt(), getIt(), getIt(), getIt(), getIt(), getIt(), getIt(), getIt(), getIt(), getIt()))
    ..registerLazySingleton(() => SearchBloc(getIt(), getIt(), getIt(), getIt(), getIt(), getIt(), getIt()))
    ..registerLazySingleton(() => MatchingLeadsBloc(getIt()))
    ..registerLazySingleton(() => MatchingPropertiesBloc(getIt(), getIt()))
    ..registerLazySingleton(() => NotificationBloc(getIt(), getIt(), getIt(), getIt(), getIt()))
    ..registerLazySingleton(() => BasicInfoBloc(getIt(), getIt(), getIt()))
    ..registerLazySingleton(() => PropertyInfoTabBloc(getIt(), getIt(), getIt(), getIt()))
    ..registerLazySingleton(() => FacilitiesBloc(getIt()))
    ..registerLazySingleton(() => GalleryTabBloc(getIt(), getIt(), getIt()))
    ..registerLazySingleton(() => SplashCubit(getIt(), getIt()))
    ..registerLazySingleton(() => BookingFormBloc(getIt(), getIt(), getIt(), getIt(), getIt(), getIt()))
    ..registerLazySingleton(() => MatchingProjectsBloc(getIt()))
    ..registerLazySingleton(() => MultiFactorAuthenticationBloc(getIt(), getIt(), getIt()))
    ..registerLazySingleton(() => WhatsappChatBloc(getIt(), getIt(), getIt(), getIt(), getIt(), getIt()))
    ..registerLazySingleton(() => CrmTemplatesBloc(getIt(), getIt(), getIt(), getIt(), getIt()))
    ..registerLazySingleton(() => WhatsappMediaPreviewBloc(getIt()))
    ..registerLazySingleton(() => CustomAddLeadBloc(getIt(), getIt(), getIt(), getIt(), getIt(), getIt(), getIt(), getIt(), getIt(), getIt(), getIt(), getIt(), getIt()))
    ..registerLazySingleton(() => ProjectInfoBloc(getIt(), getIt()))
    ..registerLazySingleton(() => ProjectFilterBloc(getIt(), getIt(), getIt(), getIt(), getIt()))
    ..registerLazySingleton(() => MatchingLeadsByProjectBloc(getIt()))
    ..registerLazySingleton(() => ManageProjectsBloc(getIt(), getIt()))
    ..registerLazySingleton(() => DashBoardBloc(getIt(), getIt(), getIt(), getIt(), getIt(), getIt(), getIt(), getIt(), getIt(), getIt(), getIt()))
    ..registerLazySingleton(() => CustomAddDataBloc(getIt(), getIt(), getIt(), getIt(), getIt(), getIt(), getIt(), getIt(), getIt(), getIt(), getIt(), getIt(), getIt()))
    ..registerLazySingleton(() => CustomDashboardBloc(getIt(), getIt(), getIt(), getIt(), getIt(), getIt(), getIt(), getIt(), getIt(), getIt(), getIt()))
    ..registerLazySingleton(() => DashboardFilterBloc())
    ..registerLazySingleton(() => PublishingTabBloc(getIt(), getIt(), getIt(), getIt(), getIt(), getIt(), getIt(), getIt()))
    ..registerLazySingleton(() => SavedFilterBloc(getIt()))
    ..registerLazySingleton(() => SaveFilterCubit(false))
    ..registerLazySingleton(() => AddPropertyListingBloc())
    ..registerLazySingleton(() => PropertyListingBasicInfoBloc(getIt(), getIt(), getIt(), getIt()))
    ..registerLazySingleton(() => PropertyListingPropertyInfoBloc())
    ..registerLazySingleton(() => PropertyListingFacilitiesBloc(getIt()))
    ..registerLazySingleton(() => PropertyListingGalleryBloc(getIt(), getIt(), getIt(), getIt()))
    ..registerLazySingleton(() => PropertyListingTabBloc(getIt(), getIt(), getIt(), getIt(), getIt(), getIt()))
    ..registerLazySingleton(() => BrevoEmailTemplateBloc(getIt(), getIt()))
    ..registerLazySingleton(() => PropertyListingLocationBloc(getIt()))
    ..registerLazySingleton(() => ManageTaskBloc(getIt(), getIt(), getIt()))
    ..registerLazySingleton(() => AddTaskBloc(getIt(), getIt(), getIt()))
    ..registerLazySingleton(() => TaskDetailsBloc(getIt(), getIt(), getIt()));
}

void _registerRepositories() {
  getIt
    ..registerLazySingleton<UserProfileRepository>(() => UserProfileRepositoryImpl(getIt()))
    ..registerLazySingleton<MasterDataRepository>(() => MasterDataRepositoryImpl(getIt(), getIt()))
    ..registerLazySingleton<GlobalSettingRepository>(() => GlobalSettingRepositoryImpl(getIt(), getIt(), getIt()))
    ..registerLazySingleton<UsersDataRepository>(() => UsersDataRepositoryImpl(getIt(), getIt(), getIt()))
    ..registerLazySingleton<SettingsRepository>(() => SettingsRepositoryImpl(getIt()))
    ..registerLazySingleton<AuthRepository>(() => AuthRepositoryImpl(getIt(), getIt()))
    ..registerLazySingleton<PropertiesRepository>(() => PropertiesRepositoryImpl(getIt(), getIt(), getIt()))
    ..registerLazySingleton<AuthTokenRepository>(() => AuthTokenRepositoryImpl(getIt(), getIt(), getIt(), getIt()))
    ..registerLazySingleton<QrCodeRepository>(() => QrCodeRepositoryImpl(getIt()))
    ..registerLazySingleton<LeadsRepository>(() => LeadsRepositoryImpl(getIt(), getIt(), getIt(), getIt()))
    ..registerLazySingleton<ProspectRepository>(() => ProspectRepositoryImpl(getIt(), getIt(), getIt(), getIt()))
    ..registerLazySingleton<AttendanceRepository>(() => AttendanceRepositoryImpl(getIt()))
    ..registerLazySingleton<FlagRepository>(() => FlagRepositoryImpl(getIt(), getIt(), getIt()))
    ..registerLazySingleton<ProjectRepository>(() => ProjectRepositoryImpl(getIt(), getIt(), getIt()))
    ..registerLazySingleton<SearchLocationRepository>(() => SearchLocationRepositoryImpl(getIt()))
    ..registerLazySingleton<DeviceRepository>(() => DeviceRepositoryImpl(getIt(), getIt()))
    ..registerLazySingleton<NotificationRepository>(() => NotificationRepositoryImpl(getIt()))
    ..registerLazySingleton<CustomAmenitiesAndAttributesRepository>(() => CustomAmenitiesAndAttributesRepositoryImpl(getIt(), getIt(), getIt()))
    ..registerLazySingleton<WhatsappRepository>(() => WhatsappRepositoryImpl(getIt()))
    ..registerLazySingleton<ListingSiteRepository>(() => ListingSiteRepositoryImpl(getIt()))
    ..registerLazySingleton<ListingManagementRepository>(() => ListingManagementRepositoryImpl(getIt()))
    ..registerLazySingleton<DashBoardRepository>(() => DashBoardRepositoryImpl(getIt()))
    ..registerLazySingleton<AppAnalysisRepository>(() => AppAnalysisRepositoryImpl(getIt(), getIt(), getIt(), getIt(), getIt()))
    ..registerLazySingleton<LeadsCardViewRepository>(() => LeadsCardViewRepositoryImpl(getIt(), getIt(), getIt()))
    ..registerLazySingleton<SavedFilterRepository>(() => SavedFilterRepositoryImpl(getIt()))
    ..registerLazySingleton<CustomEmailRepository>(() => CustomEmailRepositoryImpl(getIt()))
    ..registerLazySingleton<TasksRepository>(() => TasksRepositoryImpl(getIt()));
}

void _registerDataSources() {
  getIt
    ..registerLazySingleton<UserProfileRemoteDataSource>(() => UserProfileRemoteDataSourceImpl())
    ..registerLazySingleton<MasterDataLocalDataSource>(() => MasterDataLocalDataSourceImpl(getIt()))
    ..registerLazySingleton<MasterDataRemoteDataSource>(() => MasterDataRemoteDataSourceImpl())
    ..registerLazySingleton<GlobalSettingsLocalDataSource>(() => GlobalSettingsLocalDataSourceImpl(getIt()))
    ..registerLazySingleton<GlobalSettingRemoteDataSource>(() => GlobalSettingRemoteDataSourceImpl())
    ..registerLazySingleton<UserRemoteDataSource>(() => UserRemoteDataSourceImpl())
    ..registerLazySingleton<UsersLocalDataSource>(() => UsersLocalDataSourceImpl(getIt()))
    ..registerLazySingleton<AuthRemoteDataSource>(() => AuthRemoteDataSourceImpl())
    ..registerLazySingleton<AuthLocalDataSource>(() => AuthLocalDataSourceImpl(getIt()))
    ..registerLazySingleton<AuthTokenDataSource>(() => AuthTokenDataSourceImpl(getIt()))
    ..registerLazySingleton<AuthTokenLocalDataSource>(() => AuthTokenLocalDataSourceImpl(getIt()))
    ..registerLazySingleton<PropertiesRemoteDataSource>(() => PropertiesRemoteDataSourceImpl())
    ..registerLazySingleton<SettingsRemoteDataSource>(() => SettingsRemoteDataSourceImpl())
    ..registerLazySingleton<LeadsRemoteDataSource>(() => LeadsRemoteDataSourceImpl())
    ..registerLazySingleton<QrCodeRemoteDataSource>(() => QrCodeRemoteDataSourceImpl())
    ..registerLazySingleton<DataManagementRemoteDataSource>(() => DataManagementRemoteDataSourceImpl())
    ..registerLazySingleton<AttendanceRemoteDataSource>(() => AttendanceRemoteDataSourceImpl())
    ..registerLazySingleton<FlagRemoteDataSource>(() => FlagRemoteDataSourceImpl())
    ..registerLazySingleton<FlagLocalDataSource>(() => FlagLocalDataSourceImpl(getIt()))
    ..registerLazySingleton<ProjectRemoteDataSource>(() => ProjectRemoteDataSourceImpl())
    ..registerLazySingleton<SearchLocationDataSource>(() => SearchLocationDataSourceImpl())
    ..registerLazySingleton<DeviceDataSource>(() => DeviceDataSourceImpl())
    ..registerLazySingleton<NotificationRemoteDataSource>(() => NotificationRemoteDataSourceImpl())
    ..registerLazySingleton<CustomAmenitiesAndAttributesRemoteDataSource>(() => CustomAmenitiesAndAttributesRemoteDataSourceImpl())
    ..registerLazySingleton<CustomAmenitiesAndAttributesLocalDataSource>(() => CustomAmenitiesAndAttributesLocalDataSourceImpl(getIt()))
    ..registerLazySingleton<WhatsappRemoteDataSource>(() => WhatsappRemoteDataSourceImpl())
    ..registerLazySingleton<ListingSiteRemoteDataSource>(() => ListingSiteRemoteDataSourceImpl())
    ..registerLazySingleton<DashBoardRemoteDataSource>(() => DashBoardRemoteDataSourceImpl())
    ..registerLazySingleton<ListingManagementRemoteDataSource>(() => ListingManagementRemoteDataSourceImpl())
    ..registerLazySingleton<AppAnalysisDataSource>(() => AppAnalysisDataSourceImpl())
    ..registerLazySingleton<AppAnalysisLocalDataSource>(() => AppAnalysisLocalDataSourceImpl(getIt()))
    ..registerLazySingleton<LeadsCardViewLocalDataSource>(() => LeadsCardViewLocalDataSourceImpl(getIt()))
    ..registerLazySingleton<LeadsCardViewRemoteDataSource>(() => LeadsCardViewRemoteDataSourceImpl())
    ..registerLazySingleton<LeadsLocalDataSource>(() => LeadsLocalDataSourceImpl(getIt()))
    ..registerLazySingleton<TaskRemoteDataSource>(() => TaskRemoteDataSourceImpl())
    ..registerLazySingleton<SavedFilterRemoteDataSource>(() => SavedFilterRemoteDataSourceImpl())
    ..registerLazySingleton<DataManagementLocalDataSource>(() => DataManagementLocalDataSourceImpl(getIt()))
    ..registerLazySingleton<ProjectLocalDataSource>(() => ProjectLocalDataSourceImpl(getIt()))
    ..registerLazySingleton<PropertyLocalDataSource>(() => PropertyLocalDataSourceImpl(getIt()))
    ..registerLazySingleton<CustomEmailRemoteDataSource>(() => CustomEmailRemoteDataSourceImpl());
}

void _registerUseCase() {
  getIt
    ..registerLazySingleton(() => GetUserDetailsUseCase(getIt()))
    ..registerLazySingleton(() => UploadDocumentUseCase(getIt()))
    ..registerLazySingleton(() => DeleteDocumentUseCase(getIt()))
    ..registerLazySingleton(() => ChangePasswordUseCase(getIt()))
    ..registerLazySingleton(() => GetTenantDetailsUseCase(getIt()))
    ..registerLazySingleton(() => LoginUseCase(getIt()))
    ..registerLazySingleton(() => GetAllPropertiesUseCase(getIt()))
    ..registerLazySingleton(() => GetSettingsUserDetailsUseCase(getIt()))
    ..registerLazySingleton(() => RelinkUseCase(getIt()))
    ..registerLazySingleton(() => GetLastModifiedDateUseCase(getIt()))
    ..registerLazySingleton(() => SyncMasterDataUseCase(getIt(), getIt(), getIt(), getIt(), getIt(), getIt()))
    ..registerLazySingleton(() => GetAllInitialLeadsUseCase(getIt(), getIt()))
    ..registerLazySingleton(() => GetAllInitialCustomLeadsUseCase(getIt(), getIt()))
    ..registerLazySingleton(() => GetLeadsCommunicationUseCase(getIt()))
    ..registerLazySingleton(() => GetLeadInfoUseCase(getIt()))
    ..registerLazySingleton(() => GetLeadHistoryUseCase(getIt()))
    ..registerLazySingleton(() => GetProspectUseCase(getIt()))
    ..registerLazySingleton(() => GetProspectCountUseCase(getIt()))
    ..registerLazySingleton(() => GetProspectDetailsUseCase(getIt()))
    ..registerLazySingleton(() => GetPropertyUseCase(getIt()))
    ..registerLazySingleton(() => GetUsernameDetailsUseCase(getIt()))
    ..registerLazySingleton(() => GeneratedOtpUseCase(getIt()))
    ..registerLazySingleton(() => OtpVerificationUseCase(getIt()))
    ..registerLazySingleton(() => ResetPasswordWithOtpUseCase(getIt()))
    ..registerLazySingleton(() => GetQrCodeTemplateUseCase(getIt()))
    ..registerLazySingleton(() => GenerateQrCodeUseCase(getIt()))
    ..registerLazySingleton(() => GetMatchingLeadsUseCase(getIt()))
    ..registerLazySingleton(() => GetMatchingAssociateLeadsCountUseCase(getIt()))
    ..registerLazySingleton(() => SearchPropertiesUseCase(getIt()))
    ..registerLazySingleton(() => GetOwnerNamesOrAddressesUseCase(getIt()))
    ..registerLazySingleton(() => AttendanceHistoryUseCase(getIt()))
    ..registerLazySingleton(() => GetAttendanceLogsByUserUseCase(getIt()))
    ..registerLazySingleton(() => GetAttendanceLogsByUserTimeZoneUseCase(getIt()))
    ..registerLazySingleton(() => GetAttendanceSettingsUseCase(getIt()))
    ..registerLazySingleton(() => PostClockInAttendanceUseCase(getIt()))
    ..registerLazySingleton(() => PostClockOutAttendanceUseCase(getIt()))
    ..registerLazySingleton(() => ProspectReAssignUseCase(getIt()))
    ..registerLazySingleton(() => UpdateLeadFlagUseCase(getIt(), getIt()))
    ..registerLazySingleton(() => AddLeadDocumentsUseCase(getIt(), getIt()))
    ..registerLazySingleton(() => GetPropertyNameWithIdUseCase(getIt()))
    ..registerLazySingleton(() => GetProjectNameWithIdUseCase(getIt()))
    ..registerLazySingleton(() => GetProspectNotesUseCase(getIt()))
    ..registerLazySingleton(() => UpdateProspectNotesUseCase(getIt()))
    ..registerLazySingleton(() => GetCallThroughUseCase(getIt(), getIt()))
    ..registerLazySingleton(() => UpdateProspectActionCountUseCase(getIt()))
    ..registerLazySingleton(() => GetProspectTemplateUseCase(getIt()))
    ..registerLazySingleton(() => GetDataHistoryUseCase(getIt()))
    ..registerLazySingleton(() => GetLeadNotesUseCase(getIt()))
    ..registerLazySingleton(() => UpdateLeadNoteUseCase(getIt()))
    ..registerLazySingleton(() => DeleteLeadDocumentUseCase(getIt()))
    ..registerLazySingleton(() => UpdateLeadContactCountUseCase(getIt()))
    ..registerLazySingleton(() => GetLeadSubSourceUseCase(getIt()))
    ..registerLazySingleton(() => AddLeadUseCase(getIt()))
    ..registerLazySingleton(() => GetAllCustomStatusesUseCase(getIt()))
    ..registerLazySingleton(() => UpdateLeadStatusUseCase(getIt()))
    ..registerLazySingleton(() => SearchLocationUseCase(getIt()))
    ..registerLazySingleton(() => UpdateAppointmentsUseCase(getIt()))
    ..registerLazySingleton(() => GetAllStatusesUseCase(getIt()))
    ..registerLazySingleton(() => ReAssignLeadUseCase(getIt()))
    ..registerLazySingleton(() => UpdateConvertToLeadUseCase(getIt()))
    ..registerLazySingleton(() => GetProspectStatusUseCase(getIt()))
    ..registerLazySingleton(() => ReAssignPropertyUseCase(getIt()))
    ..registerLazySingleton(() => GetPropertyTemplatesUseCase(getIt()))
    ..registerLazySingleton(() => UpdateDataStatusUseCase(getIt()))
    ..registerLazySingleton(() => UpdateLeadUseCase(getIt()))
    ..registerLazySingleton(() => GetLeadByContactNoUseCase(getIt()))
    ..registerLazySingleton(() => SearchLeadsUseCase(getIt()))
    ..registerLazySingleton(() => GetMatchingPropertiesUseCase(getIt()))
    ..registerLazySingleton(() => GetDataSearchUseCase(getIt()))
    ..registerLazySingleton(() => GetProspectSubSourceUseCase(getIt()))
    ..registerLazySingleton(() => GetProspectLocationUseCase(getIt()))
    ..registerLazySingleton(() => GetDataByContactNoUseCase(getIt()))
    ..registerLazySingleton(() => AddDataUseCase(getIt()))
    ..registerLazySingleton(() => UpdateDataUseCase(getIt()))
    ..registerLazySingleton(() => AddPropertyUseCase(getIt()))
    ..registerLazySingleton(() => UpdatePropertyUseCase(getIt()))
    ..registerLazySingleton(() => DeletePropertyUseCase(getIt()))
    ..registerLazySingleton(() => GetAppointmentsProjectsUseCase(getIt()))
    ..registerLazySingleton(() => AddProjectsInLeadUseCase(getIt()))
    ..registerLazySingleton(() => GetNotificationUseCase(getIt()))
    ..registerLazySingleton(() => GetNotificationCountUseCase(getIt()))
    ..registerLazySingleton(() => GetProspectSourceUseCase(getIt()))
    ..registerLazySingleton(() => GetChannelPartnerNamesUseCase(getIt()))
    ..registerLazySingleton(() => UpdateNotificationStatusUseCase(getIt()))
    ..registerLazySingleton(() => ArchivePropertyUseCase(getIt()))
    ..registerLazySingleton(() => GetDataHistoryBasedOnTimeZoneUseCase(getIt()))
    ..registerLazySingleton(() => GetLeadHistoryBasedOnTimeZoneUseCase(getIt()))
    ..registerLazySingleton(() => GetMatchingProjectsUseCase(getIt()))
    ..registerLazySingleton(() => GetProjectByIdUseCase(getIt()))
    ..registerLazySingleton(() => GenerateMultiFactorAuthOtpUseCase(getIt()))
    ..registerLazySingleton(() => UpdatePropertyShareCountUseCase(getIt()))
    ..registerLazySingleton(() => VerifyMultiFactorAuthOtpUseCase(getIt()))
    ..registerLazySingleton(() => GetAllWhatsappMessagesUseCase(getIt()))
    ..registerLazySingleton(() => GetWhatsappMessageTemplatesUseCase(getIt()))
    ..registerLazySingleton(() => TwentyFourHourValidationUseCase(getIt()))
    ..registerLazySingleton(() => UpdateUserProfileUseCase(getIt()))
    ..registerLazySingleton(() => UpdateDocumentUseCase(getIt()))
    ..registerLazySingleton(() => BookedLeadUseCase(getIt()))
    ..registerLazySingleton(() => GetBuildersDetailsUseCase(getIt()))
    ..registerLazySingleton(() => DeleteProjectByIdUseCase(getIt()))
    ..registerLazySingleton(() => GetMatchingLeadsByProjectUseCase(getIt()))
    ..registerLazySingleton(() => UpdateProjectShareCountUseCase(getIt()))
    ..registerLazySingleton(() => GetAllProjectsUseCase(getIt()))
    ..registerLazySingleton(() => GetBookedLeadUseCase(getIt()))
    ..registerLazySingleton(() => GetAllPropertyListingUseCase(getIt()))
    ..registerLazySingleton(() => GetPropertyListingCountUseCase(getIt()))
    ..registerLazySingleton(() => DeListPropertiesUseCase(getIt()))
    ..registerLazySingleton(() => ListPropertiesUseCase(getIt()))
    ..registerLazySingleton(() => GetAllCustomListingSourceUseCase(getIt()))
    ..registerLazySingleton(() => GetCommunityUseCase(getIt()))
    ..registerLazySingleton(() => GetSubCommunityUseCase(getIt()))
    ..registerLazySingleton(() => GetAllListingSourceAndSiteAddressesUseCase(getIt()))
    ..registerLazySingleton(() => GetUserStatusCountUseCase(getIt()))
    ..registerLazySingleton(() => GetSiteVisitStatusUseCase(getIt()))
    ..registerLazySingleton(() => GetMeetingStatusUseCase(getIt()))
    ..registerLazySingleton(() => GetCallStatusUseCase(getIt()))
    ..registerLazySingleton(() => GetLeadReSourceUseCase(getIt()))
    ..registerLazySingleton(() => GetLeadReceivedUseCase(getIt()))
    ..registerLazySingleton(() => GetLeadPipeLineUseCase(getIt()))
    ..registerLazySingleton(() => GetLeadTrackerUseCase(getIt()))
    ..registerLazySingleton(() => GetProjectAmenitiesUseCase(getIt()))
    ..registerLazySingleton(() => CheckLeadAssignedByLeadIdUseCase(getIt()))
    ..registerLazySingleton(() => GetAllListingManagementPropertyUseCase(getIt()))
    ..registerLazySingleton(() => GetListingManagementPropertyCountUseCase(getIt()))
    ..registerLazySingleton(() => SearchPropertyListingUseCase(getIt()))
    ..registerLazySingleton(() => UpdateClickedLinkUseCase(getIt()))
    ..registerLazySingleton(() => GetLeadExcelDataUseCase(getIt()))
    ..registerLazySingleton(() => GetAdditionalPropertyValuesUseCase(getIt()))
    ..registerLazySingleton(() => GetAdditionalPropertyKeysUseCase(getIt()))
    ..registerLazySingleton(() => UpdateLeadCallLogUseCase(getIt(), getIt()))
    ..registerLazySingleton(() => UpdateProspectCallLogUseCase(getIt()))
    ..registerLazySingleton(() => GetDashboardCustomLeadSourceByUserCountUseCase(getIt()))
    ..registerLazySingleton(() => GetDashboardAllLeadCustomStatusCountUseCase(getIt()))
    ..registerLazySingleton(() => GetDashboardLeadSourceUseCase(getIt()))
    ..registerLazySingleton(() => GetDashboardUserCallsUseCase(getIt()))
    ..registerLazySingleton(() => GetDashboardUserWhatsAppMessageUseCase(getIt()))
    ..registerLazySingleton(() => GetDashboardDataByUserUseCase(getIt()))
    ..registerLazySingleton(() => UpdateLeadsCategoriesOrdersUseCase(getIt()))
    ..registerLazySingleton(() => GetDashboardCustomLeadSourceByUserUseCase(getIt()))
    ..registerLazySingleton(() => ClonePropertyUseCase(getIt()))
    ..registerLazySingleton(() => GetProspectExcelDataUseCase(getIt()))
    ..registerLazySingleton(() => UpdatePropertyAssignedToUseCase(getIt()))
    ..registerLazySingleton(() => UpdateCustomFilterUseCase(getIt()))
    ..registerLazySingleton(() => GetDataCommunicationUseCase(getIt()))
    ..registerLazySingleton(() => GetLeadPropertyModuleWiseUseCase(getIt()))
    ..registerLazySingleton(() => GetAllCustomListingSourceWithIdsUseCase(getIt()))
    ..registerLazySingleton(() => CheckProspectAssignedByProspectIdUseCase(getIt()))
    ..registerLazySingleton(() => AddPropertyListingUseCase(getIt()))
    ..registerLazySingleton(() => UpdatePropertyListingUseCase(getIt()))
    ..registerLazySingleton(() => GetPropertyListingDetailsById(getIt()))
    ..registerLazySingleton(() => AddTaskUseCase(getIt()))
    ..registerLazySingleton(() => GetTaskDetailsUseCase(getIt()))
    ..registerLazySingleton(() => GetAllTasksUseCase(getIt()))
    ..registerLazySingleton(() => UpdateTaskUseCase(getIt()))
    ..registerLazySingleton(() => GetAllTasksScheduleDatesUseCase(getIt()))
    ..registerLazySingleton(() => GetTaskOnScheduleDate(getIt()))
    ..registerLazySingleton(() => DeleteTaskUseCase(getIt()))
    ..registerLazySingleton(() => PostDeviceInfoAndPnsRegistrationUsecase(getIt()))
    ..registerLazySingleton(() => GetGalleryDropdownUseCase(repository: getIt()));
}

void _registerServices() {
  getIt
    ..registerLazySingleton<BlobStorageService>(() => BlobStorageServiceImpl())
    ..registerLazySingleton<IvrService>(() => IvrServiceImpl())
    ..registerLazySingleton<NativeImplementationService>(() => NativeImplementationServiceImpl())
    ..registerLazySingleton<PasswordService>(() => PasswordServiceImpl(getIt(), getIt()))
    ..registerLazySingleton<WebSocketService>(() => WebSocketServiceImpl())
    ..registerLazySingleton<UtilityService>(() => UtilityServiceImpl());
}

void _registerManagers() {
  getIt.registerLazySingleton<SecretManagerLocalService>(() => SecretManagerLocalServiceImpl(getIt()));
  getIt.registerLazySingleton<SharedPreferenceManager>(() => SharedPreferenceManagerImpl());
  getIt.registerLazySingleton<TokenManager>(() => TokenManager(sharedPreferenceManager: getIt<SharedPreferenceManager>()));
  getIt.registerLazySingleton<AppVersionManager>(() => AppVersionManagerImpl(getIt()));
}

void _registerMappers() {
  getIt
    ..registerLazySingleton<LeadEntityMapper>(() => LeadEntityMapper(getIt(), getIt()))
    ..registerLazySingleton<PropertyEntityMapper>(() => PropertyEntityMapper(getIt(), getIt(), getIt()))
    ..registerLazySingleton<ProjectEntityMapper>(() => ProjectEntityMapper(getIt()))
    ..registerLazySingleton<ProspectEntityMapper>(() => ProspectEntityMapper(getIt(), getIt()));
}
