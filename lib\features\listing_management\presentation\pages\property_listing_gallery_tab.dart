import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:leadrat/core_main/common/base/presentation/leadrat_stateless_widget.dart';
import 'package:leadrat/core_main/common/widgets/select_file_bottom_modal.dart';
import 'package:leadrat/core_main/common/widgets/selectable_item_bottom_sheet.dart';
import 'package:leadrat/core_main/di/injection_container.dart';
import 'package:leadrat/core_main/enums/app_enum/page_state_enum.dart';
import 'package:leadrat/core_main/extensions/media_query_extension.dart';
import 'package:leadrat/core_main/extensions/string_extension.dart';
import 'package:leadrat/core_main/resources/common/image_resources.dart';
import 'package:leadrat/core_main/resources/theme/color_palette.dart';
import 'package:leadrat/core_main/resources/theme/text_styles.dart';
import 'package:leadrat/core_main/utilities/dialog_manager.dart';
import 'package:leadrat/core_main/utilities/leadrat_custom_snackbar.dart';
import 'package:leadrat/features/listing_management/presentation/bloc/property_listing_gallery_bloc/property_listing_gallery_bloc.dart';
import 'package:leadrat/main.dart';
import 'package:reorderables/reorderables.dart';
import 'package:url_launcher/url_launcher.dart';

class PropertyListingGalleryTab extends LeadratStatelessWidget {
  final galleryTabBloc = getIt<PropertyListingGalleryBloc>();
  final TextEditingController thirdPartyController = TextEditingController();
  final TextEditingController degree360VideoUrlController = TextEditingController();

  PropertyListingGalleryTab({super.key});

  @override
  Widget buildContent(BuildContext context) {
    return BlocConsumer<PropertyListingGalleryBloc, PropertyListingGalleryState>(
      listener: (context, state) {
        if (state.pageState == PageState.loading) {
          DialogManager().showTransparentProgressDialog(context, message: galleryTabBloc.addPropertyListingModel.id == null ? "saving" : "updating");
        }
      },
      builder: (context, state) {
        return DefaultTabController(
          length: 2,
          child: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.fromLTRB(24, 10, 14, 20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (galleryTabBloc.isPropertyListingEnabled) ...[
                    Row(
                      children: [
                        Text("360 video URL", style: LexendTextStyles.lexend12Bold.copyWith(color: ColorPalette.primaryGreen)),
                        Container(
                          margin: const EdgeInsets.symmetric(horizontal: 10),
                          width: context.width(12),
                          height: 1,
                          color: ColorPalette.veryLightGray,
                        ),
                        Expanded(
                          child: TextField(
                            controller: degree360VideoUrlController,
                            keyboardType: TextInputType.url,
                            decoration: InputDecoration(
                                contentPadding: const EdgeInsets.all(10),
                                isCollapsed: true,
                                hintText: "Add 360  video URL",
                                hintStyle: LexendTextStyles.lexend9Regular.copyWith(
                                  color: ColorPalette.placeHolderTextColor,
                                ),
                                border: OutlineInputBorder(borderRadius: BorderRadius.circular(50), borderSide: const BorderSide(color: ColorPalette.veryLightGray)),
                                enabledBorder: OutlineInputBorder(borderRadius: BorderRadius.circular(50), borderSide: const BorderSide(color: ColorPalette.veryLightGray)),
                                focusedBorder: OutlineInputBorder(borderRadius: BorderRadius.circular(50), borderSide: const BorderSide(color: ColorPalette.veryLightGray)),
                                suffixIcon: IconButton(
                                  onPressed: () {
                                    galleryTabBloc.add(Add360VideoUrlEvent(degree360VideoUrlController.text));
                                    degree360VideoUrlController.text = "";
                                  },
                                  icon: const Icon(Icons.add_circle),
                                  color: ColorPalette.primaryDarkColor,
                                  visualDensity: const VisualDensity(horizontal: -4, vertical: -4),
                                )),
                          ),
                        ),
                      ],
                    ),
                    if (state.degree360VideoUrls.isNotEmpty) ...[
                      const SizedBox(height: 10),
                      SingleChildScrollView(
                          scrollDirection: Axis.horizontal,
                          child: Row(
                            children: state.degree360VideoUrls.map(
                              (item) {
                                return Padding(
                                  padding: const EdgeInsets.symmetric(horizontal: 4),
                                  child: RawChip(
                                    label: Text(item.title, style: LexendTextStyles.lexend9Light.copyWith(color: ColorPalette.primaryColor)),
                                    backgroundColor: ColorPalette.white,
                                    side: const BorderSide(color: ColorPalette.veryLightGray),
                                    shape: const StadiumBorder(),
                                    onPressed: () async {
                                      if (item.value == null) {
                                        LeadratCustomSnackbar.show(message: "Invalid 360 degree video URL", navigatorKey: MyApp.navigatorKey, type: SnackbarType.error);
                                      } else {
                                        await launchUrl(Uri.parse(item.value!));
                                      }
                                    },
                                    onDeleted: () {
                                      galleryTabBloc.add(Remove360VideoUrlEvent(item));
                                    },
                                    deleteIcon: const Icon(Icons.remove_circle_outline_rounded, color: ColorPalette.lightGray),
                                  ),
                                );
                              },
                            ).toList(),
                          )),
                    ],
                    const SizedBox(height: 10),
                    Row(
                      children: [
                        Text("Third Party URL", style: LexendTextStyles.lexend12Bold.copyWith(color: ColorPalette.primaryGreen)),
                        Container(
                          margin: const EdgeInsets.symmetric(horizontal: 10),
                          width: context.width(12),
                          height: 1,
                          color: ColorPalette.veryLightGray,
                        ),
                        Expanded(
                          child: TextField(
                            controller: thirdPartyController,
                            keyboardType: TextInputType.url,
                            decoration: InputDecoration(
                                contentPadding: const EdgeInsets.all(10),
                                isCollapsed: true,
                                hintText: "Third Party URL",
                                hintStyle: LexendTextStyles.lexend9Regular.copyWith(
                                  color: ColorPalette.placeHolderTextColor,
                                ),
                                border: OutlineInputBorder(borderRadius: BorderRadius.circular(50), borderSide: const BorderSide(color: ColorPalette.veryLightGray)),
                                enabledBorder: OutlineInputBorder(borderRadius: BorderRadius.circular(50), borderSide: const BorderSide(color: ColorPalette.veryLightGray)),
                                focusedBorder: OutlineInputBorder(borderRadius: BorderRadius.circular(50), borderSide: const BorderSide(color: ColorPalette.veryLightGray)),
                                suffixIcon: IconButton(
                                  onPressed: () {
                                    galleryTabBloc.add(AddThirdPartyVideoUrlEvent(thirdPartyController.text));
                                    thirdPartyController.text = "";
                                  },
                                  icon: const Icon(Icons.add_circle),
                                  color: ColorPalette.primaryDarkColor,
                                  visualDensity: const VisualDensity(horizontal: -4, vertical: -4),
                                )),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 10),
                    if (state.thirdPartyUrls.isNotEmpty) ...[
                      SingleChildScrollView(
                          scrollDirection: Axis.horizontal,
                          child: Row(
                            children: state.thirdPartyUrls.map(
                              (item) {
                                return Padding(
                                  padding: const EdgeInsets.symmetric(horizontal: 4),
                                  child: RawChip(
                                    label: Text(item.title, style: LexendTextStyles.lexend9Light.copyWith(color: ColorPalette.primaryColor)),
                                    backgroundColor: ColorPalette.white,
                                    side: const BorderSide(color: ColorPalette.veryLightGray),
                                    shape: const StadiumBorder(),
                                    onPressed: () async {
                                      if (item.value == null) {
                                        LeadratCustomSnackbar.show(message: "Invalid Third Party URL", navigatorKey: MyApp.navigatorKey, type: SnackbarType.error);
                                      } else {
                                        await launchUrl(Uri.parse(item.value!));
                                      }
                                    },
                                    onDeleted: () {
                                      galleryTabBloc.add(RemoveThirdPartyVideoUrlEvent(item));
                                    },
                                    deleteIcon: const Icon(Icons.remove_circle_outline_rounded, color: ColorPalette.lightGray),
                                  ),
                                );
                              },
                            ).toList(),
                          )),
                      const SizedBox(height: 10),
                    ],
                    //water mark widget
                    // const SizedBox(height: 10),
                    // Row(children: [
                    //   Text("Add water mark", style: LexendTextStyles.lexend12Bold.copyWith(color: ColorPalette.primaryGreen)),
                    //   const SizedBox(width: 10),
                    //   Checkbox(
                    //     visualDensity: const VisualDensity(horizontal: -4, vertical: -4),
                    //     value: state.isWaterMarkEnabled,
                    //     onChanged: (value) {
                    //       galleryTabBloc.add(WaterMarkEvent());
                    //     },
                    //     activeColor: ColorPalette.primaryGreen,
                    //   )
                    // ]),
                  ],
                  TabBar(
                    labelStyle: LexendTextStyles.lexend14SemiBold.copyWith(color: ColorPalette.primaryDarkColor),
                    labelColor: ColorPalette.primaryDarkColor,
                    unselectedLabelColor: ColorPalette.gray100,
                    indicator: const BoxDecoration(
                      border: Border(
                        bottom: BorderSide(
                          color: Colors.black, // Set your underline color here
                          width: 2.0, // Set the thickness of the underline
                        ),
                      ),
                    ),
                    dividerColor: ColorPalette.transparent,
                    tabs: const [
                      Tab(text: 'photos'),
                      // Tab(text: 'videos'),
                      Tab(text: 'brochures'),
                    ],
                  ),
                  SizedBox(
                    height: context.height(80),
                    child: TabBarView(
                      children: [
                        _photosTab(context, state),
                        // _videosTab(),
                        _brochuresTab(context, state),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _photosTab(BuildContext context, PropertyListingGalleryState state) {
    return SingleChildScrollView(
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(0, 10, 0, 10),
            child: state.photos?.isNotEmpty ?? false
                ? Stack(
                    children: [
                      Container(
                        height: context.height(40), // 40% of the screen height
                        width: context.width(100),
                        decoration: BoxDecoration(
                          border: Border.all(
                            color: ColorPalette.gray400,
                          ),
                          borderRadius: BorderRadius.circular(10),
                          color: ColorPalette.transparent,
                        ),
                        child: Padding(
                          padding: const EdgeInsets.only(top: 8, left: 8, bottom: 55, right: 8),
                          child: SingleChildScrollView(
                            child: ReorderableWrap(
                              spacing: 10,
                              runSpacing: 10,
                              needsLongPressDraggable: true,
                              onReorder: (int oldIndex, int newIndex) {
                                final photo = state.photos?.removeAt(oldIndex);
                                if (photo != null) state.photos?.insert(newIndex, photo);
                                galleryTabBloc.add(UpdatePhotoOrderEvent(List<SelectableItem<File>>.from(state.photos!)));
                              },
                              children: List.generate(state.photos!.length, (index) {
                                final photo = state.photos![index];
                                final imagePath = photo.value?.path ?? '';
                                final currentCategory = state.imageCategories[imagePath] ?? (state.galleryDropdownItems.isNotEmpty ? state.galleryDropdownItems.first : 'Images');

                                return SizedBox(
                                  width: 120,
                                  child: Column(
                                    children: [
                                      SizedBox(
                                        width: 100,
                                        height: 100,
                                        child: Stack(children: [
                                          if (!(photo.isEnabled)) // here is enabled act as uploaded
                                            Image.file(
                                              photo.value ?? File(''),
                                            ),
                                          if (photo.isEnabled) CachedNetworkImage(imageUrl: photo.value?.path.appendWithImageBaseUrl() ?? ''),
                                          Positioned(
                                            bottom: 0,
                                            right: 3,
                                            child: Container(
                                              padding: const EdgeInsets.all(2),
                                              decoration: const BoxDecoration(
                                                color: ColorPalette.primaryLightColor,
                                                borderRadius: BorderRadius.all(Radius.circular(5)),
                                              ),
                                              child: Row(
                                                children: [
                                                  if (state.galleryDropdownItems.isNotEmpty)
                                                    SelectableItemBottomSheet<String>(
                                                      title: "Select Category",
                                                      selectableItems: state.galleryDropdownItems
                                                          .map((item) => SelectableItem<String>(
                                                                title: item,
                                                                value: item,
                                                                isSelected: item == currentCategory,
                                                              ))
                                                          .toList(),
                                                      selectedItem: SelectableItem<String>(
                                                        title: currentCategory,
                                                        value: currentCategory,
                                                        isSelected: true,
                                                      ),
                                                      onItemSelected: (selectedItem) {
                                                        galleryTabBloc.add(UpdateImageCategoryEvent(
                                                          imagePath: imagePath,
                                                          category: selectedItem.value ?? selectedItem.title,
                                                        ));
                                                      },
                                                      canAddNewItems: true,
                                                      canSearchItems: true,
                                                      child: Row(
                                                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                        children: [
                                                          Text(SelectableItem<String>(
                                                            title: currentCategory,
                                                            value: currentCategory,
                                                            isSelected: true,
                                                          ), style: LexendTextStyles.lexend10SemiBold.copyWith(color: ColorPalette.primary)),
                                                          const Icon(Icons.keyboard_arrow_down, size: 25, color: ColorPalette.primary),
                                                        ],
                                                      ),
                                                    ),
                                                  GestureDetector(
                                                    onTap: () {
                                                      galleryTabBloc.add(RemovePhotosEvent(photo.value?.path));
                                                    },
                                                    child: Container(
                                                      height: 20,
                                                      width: 20,
                                                      decoration: BoxDecoration(
                                                        borderRadius: BorderRadius.circular(10),
                                                        color: ColorPalette.superSilver,
                                                      ),
                                                      child: const Icon(
                                                        Icons.delete_outline,
                                                        size: 15,
                                                      ),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                        ]),
                                      ),
                                      const SizedBox(height: 5),
                                    ],
                                  ),
                                );
                              }),
                            ),
                          ),
                        ),
                      ),
                      Positioned(
                        bottom: 10,
                        right: 10,
                        child: GestureDetector(
                          onTap: () {
                            selectFileBottomModal(
                              context,
                              (selectedOption) {
                                galleryTabBloc.add(AddPhotosEvent(selectFileEnum: selectedOption));
                              },
                            );
                          },
                          child: Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(10),
                              color: ColorPalette.primaryGreen,
                            ),
                            child: Padding(
                              padding: const EdgeInsets.fromLTRB(15, 8, 15, 8),
                              child: Row(
                                children: [
                                  const Icon(
                                    Icons.file_upload_outlined,
                                    color: ColorPalette.white,
                                    size: 15,
                                  ),
                                  const SizedBox(width: 6),
                                  Text(
                                    'upload',
                                    style: LexendTextStyles.lexend13SemiBold.copyWith(color: ColorPalette.white),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  )
                : GestureDetector(
                    onTap: () {
                      selectFileBottomModal(
                        context,
                        (selectedOption) {
                          galleryTabBloc.add(AddPhotosEvent(selectFileEnum: selectedOption));
                        },
                      );
                    },
                    child: Container(
                      height: context.height(30),
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: ColorPalette.gray400,
                        ),
                        borderRadius: BorderRadius.circular(10),
                        color: ColorPalette.transparent,
                      ),
                      child: Center(
                        child: Stack(
                          children: [
                            Image.asset(
                              LottieResources.uploadDocumentsAnimation,
                              height: context.height(25),
                              width: context.width(55),
                            ),
                            Positioned(
                              bottom: context.height(0.9),
                              child: Column(
                                children: [
                                  Text(
                                    '  + Add Photos',
                                    style: LexendTextStyles.lexend18SemiBold.copyWith(color: ColorPalette.primaryDarkColor),
                                  ),
                                  Text(
                                    '  (max limit 50 mb per image)',
                                    style: LexendTextStyles.lexend14Light.copyWith(color: ColorPalette.gray200),
                                  )
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
          ),
          _description(), // No overlap will occur now
        ],
      ),
    );
  }

  Widget _brochuresTab(BuildContext context, PropertyListingGalleryState state) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(0, 10, 0, 10),
            child: state.brochures?.isNotEmpty ?? false
                ? Stack(
                    children: [
                      // SizedBox with a height of 40% of the screen
                      Container(
                        height: context.height(40), // 40% of the screen height
                        width: context.width(100),
                        decoration: BoxDecoration(
                          border: Border.all(
                            color: ColorPalette.gray400,
                          ),
                          borderRadius: BorderRadius.circular(10),
                          color: ColorPalette.transparent,
                        ),
                        child: Padding(
                          padding: const EdgeInsets.only(top: 8, left: 8, bottom: 55, right: 8),
                          child: SingleChildScrollView(
                            child: Wrap(
                              spacing: 10, // Horizontal space between images
                              runSpacing: 10, // Vertical space between rows
                              children: List.generate(state.brochures!.length, (index) {
                                return Container(
                                  height: 40,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(4),
                                    color: ColorPalette.superSilver,
                                  ),
                                  child: Padding(
                                    padding: const EdgeInsets.fromLTRB(8, 4, 8, 4),
                                    child: Row(
                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                      children: [
                                        Row(
                                          children: [
                                            const Icon(
                                              Icons.picture_as_pdf_rounded,
                                              size: 20,
                                              color: ColorPalette.mediumRed,
                                            ),
                                            const SizedBox(
                                              width: 10,
                                            ),
                                            SizedBox(
                                              width: context.width(60),
                                              child: Text(
                                                state.brochures![index].title,
                                                maxLines: 1,
                                                style: LexendTextStyles.lexend14Medium.copyWith(color: ColorPalette.primaryDarkColor),
                                              ),
                                            ),
                                          ],
                                        ),
                                        GestureDetector(
                                          onTap: () {
                                            galleryTabBloc.add(RemoveBrochuresEvent(state.brochures?[index].value?.path));
                                          },
                                          child: Container(
                                            decoration: BoxDecoration(
                                              borderRadius: BorderRadius.circular(10),
                                              color: ColorPalette.mediumRed,
                                            ),
                                            height: 20,
                                            width: 20,
                                            child: const Icon(
                                              Icons.delete_outline_rounded,
                                              color: ColorPalette.white,
                                              size: 10,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                );
                              }),
                            ),
                          ),
                        ),
                      ),
                      Positioned(
                        bottom: 10, // Adjust the distance from the bottom
                        right: 10, // Adjust the distance from the right side
                        child: GestureDetector(
                          onTap: () {
                            selectFileBottomModal(
                              context,
                              isDocument: true,
                              (selectedOption) {
                                galleryTabBloc.add(AddBrochuresEvent(selectedOption));
                              },
                            );
                          },
                          child: Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(10),
                              color: ColorPalette.primaryGreen,
                            ),
                            child: Padding(
                              padding: const EdgeInsets.fromLTRB(15, 8, 15, 8),
                              child: Row(
                                children: [
                                  const Icon(
                                    Icons.file_upload_outlined,
                                    color: ColorPalette.white,
                                    size: 15,
                                  ),
                                  const SizedBox(width: 6),
                                  Text(
                                    'upload',
                                    style: LexendTextStyles.lexend13SemiBold.copyWith(color: ColorPalette.white),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  )
                : GestureDetector(
                    onTap: () {
                      selectFileBottomModal(
                        context,
                        isDocument: true,
                        (selectedOption) {
                          galleryTabBloc.add(AddBrochuresEvent(selectedOption));
                        },
                      );
                    },
                    child: Container(
                      height: context.height(30),
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: ColorPalette.gray400,
                        ),
                        borderRadius: BorderRadius.circular(10),
                        color: ColorPalette.transparent,
                      ),
                      child: Center(
                        child: Stack(
                          children: [
                            Image.asset(
                              LottieResources.uploadDocumentsAnimation,
                              height: context.height(25),
                              width: context.width(55),
                            ),
                            Positioned(
                              bottom: context.height(0.9),
                              child: Column(
                                children: [
                                  Text(
                                    '+ Add Brochures  ',
                                    style: LexendTextStyles.lexend18SemiBold.copyWith(color: ColorPalette.primaryDarkColor),
                                  ),
                                  Text(
                                    '(max limit 5 mb per document)',
                                    style: LexendTextStyles.lexend14Light.copyWith(color: ColorPalette.gray200),
                                  )
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
          ),
          _description(
            isBrochures: true,
            noteWidget: RichText(
              text: TextSpan(
                style: LexendTextStyles.lexend12Medium.copyWith(color: ColorPalette.gray600),
                children: [
                  const TextSpan(text: "Only "),
                  TextSpan(text: ".pdf ", style: LexendTextStyles.lexend12SemiBold.copyWith(color: ColorPalette.black)),
                  const TextSpan(text: "file formats are acceptable"),
                ],
              ),
            ), // No overlap will occur now
          ), // No overlap will occur now
        ],
      ),
    );
  }

  Widget _description({Widget? noteWidget, isBrochures = false}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Note: ',
              style: LexendTextStyles.lexend13SemiBold.copyWith(color: ColorPalette.primaryGreen),
            ),
            const SizedBox(
              height: 4,
            ),
            noteWidget ??
                RichText(
                  text: TextSpan(
                    style: LexendTextStyles.lexend12Medium.copyWith(color: ColorPalette.gray600),
                    children: [
                      const TextSpan(text: "Only "),
                      TextSpan(text: ".jpg", style: LexendTextStyles.lexend12SemiBold.copyWith(color: ColorPalette.black)),
                      const TextSpan(text: ", "),
                      TextSpan(text: ".jpeg", style: LexendTextStyles.lexend12SemiBold.copyWith(color: ColorPalette.black)),
                      const TextSpan(text: ", "),
                      TextSpan(text: ".png ", style: LexendTextStyles.lexend12SemiBold.copyWith(color: ColorPalette.black)),
                      const TextSpan(text: "file formats are acceptable.\nFor best view please upload the image with approx size of 1920 x 1080 size in pixels. "),
                    ],
                  ),
                ),
          ],
        ),
        if (!isBrochures) ...[
          const SizedBox(
            height: 10,
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Suggestions: ',
                style: LexendTextStyles.lexend13SemiBold.copyWith(color: ColorPalette.primaryGreen, height: 1.7),
              ),
              RichText(
                text: TextSpan(
                  style: LexendTextStyles.lexend12Medium.copyWith(color: ColorPalette.gray600),
                  children: const [
                    TextSpan(text: "1. Add photos by clicking using your device camera or browsing in device files.\n\n"),
                    TextSpan(text: "2. Photos that will make your property microsite better and attractive:\n"),
                    TextSpan(text: "  • Hall\n  • Bedrooms\n  • Bathrooms\n  • Kitchens\n  • Dining Hall\n  • Balcony\n  • Outside view"),
                  ],
                ),
              ),
            ],
          ),
        ]
      ],
    );
  }
}
