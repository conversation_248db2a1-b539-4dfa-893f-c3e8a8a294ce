import 'package:collection/collection.dart';
import 'package:fpdart/fpdart.dart';
import 'package:leadrat/core_main/common/data/master_data/data_source/local/masterdata_local_data_source.dart';
import 'package:leadrat/core_main/common/data/master_data/models/modified_date_model.dart';
import 'package:leadrat/core_main/common/data/user/models/user_details_model.dart';
import 'package:leadrat/core_main/common/data/user/repository/users_repository.dart';
import 'package:leadrat/core_main/enums/app_enum/app_module.dart';
import 'package:leadrat/core_main/enums/app_enum/command_type.dart';
import 'package:leadrat/core_main/enums/app_enum/entity_type_enum.dart';
import 'package:leadrat/core_main/enums/property_enums/property_status.dart';
import 'package:leadrat/core_main/extensions/object_extension.dart';
import 'package:leadrat/core_main/remote/rest_response/paged_rest_response.dart';
import 'package:leadrat/core_main/utilities/template_utils.dart';
import 'package:leadrat/core_main/utilities/type_def.dart';
import 'package:leadrat/features/lead/domain/entities/get_lead_entity.dart';
import 'package:leadrat/features/properties/data/data_source/local/properties_local_data_source.dart';
import 'package:leadrat/features/properties/data/models/add_property_model.dart';
import 'package:leadrat/features/properties/data/models/gallery_dropdown_model.dart';
import 'package:leadrat/features/properties/data/models/list_property_model.dart';
import 'package:leadrat/features/properties/data/models/properties_with_id_model.dart';
import 'package:leadrat/features/properties/data/models/property_filter_model.dart';
import 'package:leadrat/features/properties/data/models/re_assign_property_model.dart';
import 'package:leadrat/features/properties/data/models/update_property_assignedTo_model.dart';
import 'package:leadrat/features/properties/data/models/update_property_share_count_model.dart';
import 'package:leadrat/features/properties/domain/entities/get_property_entity.dart';
import 'package:leadrat/features/properties/domain/entities/property_lead_count_entity.dart';

import '../../../../core_main/errors/failure.dart';
import '../../domain/entities/get_all_property_entity.dart';
import '../../domain/repository/properties_repository.dart';
import '../data_source/properties_remote_data_source.dart';

class PropertiesRepositoryImpl implements PropertiesRepository {
  final PropertiesRemoteDataSource _propertiesRemoteDataSource;
  final MasterDataLocalDataSource _masterLocalDataSource;
  final PropertyLocalDataSource _propertyLocalDataSource;

  PropertiesRepositoryImpl(this._propertiesRemoteDataSource, this._propertyLocalDataSource, this._masterLocalDataSource);

  @override
  FutureEitherFailure<PagedResponse<GetAllPropertyEntity, String>?> getAllProperties(int pageNumber, PropertyFilterModel? propertyFilterModel) async {
    try {
      final result = await _propertiesRemoteDataSource.getAllProperties(pageNumber, propertyFilterModel);
      return right(
        PagedResponse<GetAllPropertyEntity, String>(
          items: result?.items.map((e) => e.toEntity()).toList() ?? [],
          succeeded: result?.succeeded ?? false,
          data: null,
          totalCount: result?.totalCount ?? 0,
        ),
      );
    } catch (exception) {
      return left(Failure(exception.toString()));
    }
  }

  @override
  FutureEitherFailure<PagedResponse<GetAllPropertyEntity, String>?> searchProperties(String searchText, int pageNumber, {bool isPropertyListingEnabled = false}) async {
    try {
      final result = await _propertiesRemoteDataSource.searchProperties(searchText, pageNumber, isPropertyListingEnabled: isPropertyListingEnabled);
      return right(
        PagedResponse<GetAllPropertyEntity, String>(
          items: result?.items.map((e) => e.toEntity()).toList() ?? [],
          succeeded: result?.succeeded ?? false,
          data: null,
          totalCount: result?.totalCount ?? 0,
        ),
      );
    } catch (exception) {
      return left(Failure(exception.toString()));
    }
  }

  @override
  FutureEitherFailure<GetPropertyEntity?> getPropertyById(String propertyId) async {
    try {
      final result = await _propertiesRemoteDataSource.getPropertyById(propertyId);
      return right(result?.toEntity());
    } catch (exception) {
      return left(Failure(exception.toString()));
    }
  }

  @override
  FutureEitherFailure<List<PropertyLeadCountEntity?>?> getMatchingAssociateLeadsCount(String propertyId) async {
    try {
      final result = await _propertiesRemoteDataSource.getMatchingAssociateLeadsCount(propertyId);
      return right(result
          ?.map(
            (e) => e?.toEntity(),
          )
          .toList());
    } catch (exception) {
      return left(Failure(exception.toString()));
    }
  }

  @override
  FutureEitherFailure<PagedResponse<LeadWithDegreeMatchedEntity, String>?> getMatchingLeads({int? pageNumber, int? pageSize, required String propertyId, bool? isPropertyListingEnabled}) async {
    try {
      final result = await _propertiesRemoteDataSource.getMatchingLeads(pageSize: pageSize, propertyId: propertyId, pageNumber: pageNumber, isPropertyListingEnabled: isPropertyListingEnabled);
      return right(PagedResponse<LeadWithDegreeMatchedEntity, String>(
        items: result?.items.map((e) => e.toEntity()).toList() ?? [],
        succeeded: result?.succeeded ?? false,
        data: null,
        totalCount: result?.totalCount ?? 0,
      ));
    } catch (exception) {
      return left(Failure(exception.toString()));
    }
  }

  @override
  FutureEitherFailure<List<String>?> getOwnerNames() async {
    try {
      final result = await _propertiesRemoteDataSource.getOwnerNames();
      return right(result);
    } catch (exception) {
      return left(Failure(exception.toString()));
    }
  }

  @override
  FutureEitherFailure<List<PropertiesWithIdModel>?> getPropertyNameWithId({bool restore = false, Duration duration = const Duration(seconds: 60)}) async {
    try {
      final lastModifiedDateList = _masterLocalDataSource.getModifiedDateModels();

      var lastModifiedModel = lastModifiedDateList.firstWhereOrNull(
            (element) => element.entityType == EntityTypeEnum.property,
          ) ??
          ModifiedDateModel(
            entityType: EntityTypeEnum.property,
            lastModifiedDate: DateTime.now(),
            lastUpdatedLocallyDate: DateTime.utc(1, 1, 1),
          );

      final localData = _propertyLocalDataSource.getPropertyNameWithId();

      if (restore||(localData == null || localData.isEmpty) || localData.isEmpty || (lastModifiedModel.lastModifiedDate?.isAfter(lastModifiedModel.lastUpdatedLocallyDate ?? DateTime.utc(1, 1, 1)) ?? true)) {
        final response = await _propertiesRemoteDataSource.getPropertyNameWithId();
        if (response != null) {
          await _propertyLocalDataSource.savePropertyNameWithId(response);
          lastModifiedModel = lastModifiedModel.copyWith(lastUpdatedLocallyDate: DateTime.now());
          await _masterLocalDataSource.updateModifiedDateModel(lastModifiedModel);
          return right(response);
        }
      }

      return right(localData);
    } catch (e, stackTrace) {
      e.logException(stackTrace);
      return left(Failure(e.toString()));
    }
  }

  @override
  FutureEitherFailure<List<String>?> getAddresses() async {
    try {
      final result = await _propertiesRemoteDataSource.getAddresses();
      return right(result);
    } catch (exception) {
      return left(Failure(exception.toString()));
    }
  }

  @override
  Future<bool?> reAssignProperty(ReAssignPropertyModel? reAssignModel) async {
    try {
      return await _propertiesRemoteDataSource.reAssignProperty(reAssignModel);
    } catch (exception) {
      return false;
    }
  }

  @override
  Future<bool?> updatePropertyAssignedTo(UpdatePropertyAssignedToModel? updatePropertyAssignedToModel) async {
    try {
      return await _propertiesRemoteDataSource.updatePropertyAssignedTo(updatePropertyAssignedToModel);
    } catch (exception) {
      return false;
    }
  }

  @override
  Future<AddPropertyModel?> addProperty(AddPropertyModel? addPropertyModel) async {
    try {
      final response = await _propertiesRemoteDataSource.addProperty(addPropertyModel);
      if (response != null) await _updatePropertyModifiedDate();
      return response;
    } catch (exception) {
      return null;
    }
  }

  @override
  Future<AddPropertyModel?> updateProperty(AddPropertyModel? updatePropertyModel) async {
    try {
      final response = await _propertiesRemoteDataSource.updateProperty(updatePropertyModel);
      if (response != null) await _updatePropertyModifiedDate();
      return response;
    } catch (exception) {
      return null;
    }
  }

  @override
  Future<String?> deleteProperty(String? propertyId) async {
    try {
      final response = await _propertiesRemoteDataSource.deleteProperty(propertyId);
      if (response != null) await _updatePropertyModifiedDate();
      return response;
    } catch (exception) {
      return null;
    }
  }

  @override
  FutureEitherFailure<bool?> archiveProperty(List<String?> archivePropertyList) async {
    try {
      final result = await _propertiesRemoteDataSource.archiveProperty(archivePropertyList);
      if (result != null) await _updatePropertyModifiedDate();
      return right(result);
    } catch (exception) {
      return left(Failure(exception.toString()));
    }
  }

  @override
  FutureEitherFailure<bool?> updatePropertyShareCount(UpdatePropertyShareCountModel updatePropertyShareCountModel) async {
    try {
      final result = await _propertiesRemoteDataSource.updatePropertyShareCount(updatePropertyShareCountModel);
      return right(result);
    } catch (exception) {
      return left(Failure(exception.toString()));
    }
  }

  @override
  FutureEitherFailure<Map<PropertyVisibility, int>?> getPropertyListingCount({PropertyFilterModel? propertyFilterModel}) async {
    try {
      final result = await _propertiesRemoteDataSource.getPropertyListingCount(propertyFilterModel: propertyFilterModel);
      return right(result);
    } catch (exception) {
      return left(Failure(exception.toString()));
    }
  }

  @override
  FutureEitherFailure<PagedResponse<GetAllPropertyEntity, String>?> getAllPropertyListing(int pageNumber, PropertyFilterModel? propertyFilterModel, [int pageSize = 10]) async {
    try {
      final result = await _propertiesRemoteDataSource.getAllPropertyListing(pageNumber, propertyFilterModel, pageSize);
      return right(
        PagedResponse<GetAllPropertyEntity, String>(
          items: result?.items.map((e) => e.toEntity()).toList() ?? [],
          succeeded: result?.succeeded ?? false,
          data: null,
          totalCount: result?.totalCount ?? 0,
        ),
      );
    } catch (exception) {
      return left(Failure(exception.toString()));
    }
  }

  @override
  FutureEitherFailure<bool?> deListProperties(ListPropertyModel models) async {
    try {
      final result = await _propertiesRemoteDataSource.deListProperties(models);
      return right(result);
    } catch (exception) {
      return left(Failure(exception.toString()));
    }
  }

  @override
  FutureEitherFailure<bool?> listProperties(ListPropertyModel models) async {
    try {
      final result = await _propertiesRemoteDataSource.listProperties(models);
      return right(result);
    } catch (exception) {
      return left(Failure(exception.toString()));
    }
  }

  @override
  Future<List<String>?> getCurrencies() async {
    try {
      return await _propertiesRemoteDataSource.getCurrencies();
    } catch (ex) {
      return null;
    }
  }

  @override
  FutureEitherFailure<String> getTemplateMessageByProperty({String? header, String? message, String? footer, UserDetailsModel? userDetails, String? domain, List<GetPropertyEntity?>? propertyEntity, String? leadName}) async {
    if (propertyEntity == null || propertyEntity.isEmpty) {
      return left(Failure('unable get lead templates'));
    }
    String actualMessage = '';
    if (header != null && header.isNotEmpty && propertyEntity.isNotEmpty) {
      var firstEntity = await getPropertyById(propertyEntity.first?.id ?? '');
      firstEntity.fold((failure) {}, (entity) {
        actualMessage += '${TemplateUtils.getPropertyMessage(entity ?? GetPropertyEntity(), header, userDetails, domain, leadName)}\n\n';
      });
    }
    bool hasMultipleProperties = propertyEntity.length > 1;
    for (int i = 0; i < propertyEntity.length; i++) {
      if (message == null || message.isEmpty) continue;
      var entity = await getPropertyById(propertyEntity[i]?.id ?? '');
      entity.fold((failure) {}, (entity) {
        if (hasMultipleProperties) {
          actualMessage += '${i + 1}. ';
        }
        actualMessage += TemplateUtils.getPropertyMessage(entity ?? GetPropertyEntity(), message, userDetails, domain, leadName);
        if (i < propertyEntity.length - 1) {
          actualMessage += '\n';
        }
      });
    }

    if (footer != null && footer.isNotEmpty && propertyEntity.isNotEmpty) {
      var lastEntity = await getPropertyById(propertyEntity.last?.id ?? '');
      lastEntity.fold((failure) {}, (entity) {
        actualMessage += '\n\n${TemplateUtils.getPropertyMessage(entity ?? GetPropertyEntity(), footer, userDetails, domain, leadName)}';
      });
    }

    if (actualMessage.isEmpty) {
      return left(Failure('unable get lead templates'));
    } else {
      return right(actualMessage);
    }
  }

  Future<void> _updatePropertyModifiedDate() async {
    final lastModifiedDateList = _masterLocalDataSource.getModifiedDateModels();

    var lastModifiedModel = lastModifiedDateList.firstWhereOrNull(
          (element) => element.entityType == EntityTypeEnum.property,
        ) ??
        ModifiedDateModel(
          entityType: EntityTypeEnum.property,
          lastModifiedDate: DateTime.now(),
          lastUpdatedLocallyDate: DateTime.utc(1, 1, 1),
        );

    lastModifiedModel = lastModifiedModel.copyWith(lastModifiedDate: DateTime.now());
    await _masterLocalDataSource.updateModifiedDateModel(lastModifiedModel);
  }

  @override
  Future<Either<Failure, GalleryDropdownModel>> getGalleryDropdownData() async {
    try {
      final result = await _propertiesRemoteDataSource.getGalleryDropdownData();
      return Either.right(result);
    } catch (e) {
      return Either.left(Failure(e.toString()));
    }
  }
}
