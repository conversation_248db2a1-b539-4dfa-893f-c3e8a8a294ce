import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:ui' as ui;

import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:image_picker/image_picker.dart';
import 'package:leadrat/core_main/common/base/models/item_simple_model.dart';
import 'package:leadrat/core_main/common/constants/constants.dart';
import 'package:leadrat/core_main/common/widgets/selectable_item_bottom_sheet.dart';
import 'package:leadrat/core_main/di/injection_container.dart';
import 'package:leadrat/core_main/enums/app_enum/blob_folder_names.dart';
import 'package:leadrat/core_main/enums/app_enum/file_type.dart';
import 'package:leadrat/core_main/enums/app_enum/page_state_enum.dart';
import 'package:leadrat/core_main/enums/app_enum/select_file_enum.dart';
import 'package:leadrat/core_main/enums/common/possession_type.dart';
import 'package:leadrat/core_main/enums/property_listing/compliance_type.dart';
import 'package:leadrat/core_main/extensions/string_extension.dart';
import 'package:leadrat/core_main/extensions/time_zone_extension.dart';
import 'package:leadrat/core_main/utilities/dialog_manager.dart';
import 'package:leadrat/core_main/utilities/file_picker_util.dart';
import 'package:leadrat/core_main/utilities/leadrat_custom_snackbar.dart';
import 'package:leadrat/core_main/utilities/property_utils.dart';
import 'package:leadrat/features/home/<USER>/bloc/leadrat_home_bloc/leadrat_home_bloc.dart';
import 'package:leadrat/features/listing_management/data/models/add_property_listing_model.dart';
import 'package:leadrat/features/listing_management/domain/usecase/add_property_listing_use_case.dart';
import 'package:leadrat/features/listing_management/domain/usecase/update_property_listing_use_case.dart';
import 'package:leadrat/features/listing_management/presentation/bloc/add_property_listing_bloc/add_property_listing_bloc.dart';
import 'package:leadrat/features/listing_management/presentation/bloc/property_listing_basic_info_bloc/property_listing_basic_info_bloc.dart';
import 'package:leadrat/features/listing_management/presentation/bloc/property_listing_facilities_bloc/property_listing_facilities_bloc.dart';
import 'package:leadrat/features/listing_management/presentation/bloc/property_listing_tab_bloc/property_listing_tab_bloc.dart';
import 'package:leadrat/features/listing_management/presentation/bloc/propety_listing_property_info_bloc/property_listing_property_info_bloc.dart';
import 'package:leadrat/features/properties/data/models/property_attribute_model.dart';
import 'package:leadrat/features/properties/data/models/property_brochure_model.dart';
import 'package:leadrat/features/properties/data/models/property_dimension_model.dart';
import 'package:leadrat/features/properties/data/models/property_image_model.dart';
import 'package:leadrat/features/properties/domain/usecase/get_gallery_dropdown_usecase.dart';
import 'package:leadrat/features/user_profile/domain/usecase/upload_document_usecase.dart';
import 'package:leadrat/core_main/common/base/usecase/use_case.dart';
import 'package:leadrat/main.dart';
import 'package:url_launcher/url_launcher.dart';

part 'property_listing_gallery_event.dart';

part 'property_listing_gallery_state.dart';

class PropertyListingGalleryBloc extends Bloc<PropertyListingGalleryEvent, PropertyListingGalleryState> {
  AddPropertyListingModel addPropertyListingModel = AddPropertyListingModel(monetaryInfo: PropertyListingMonetaryInfoModel());

  final UploadDocumentUseCase _uploadDocumentUseCase;
  final AddPropertyListingUseCase _addPropertyListingUseCase;
  final UpdatePropertyListingUseCase _updatePropertyUseCase;
  final GetGalleryDropdownUseCase _getGalleryDropdownUseCase;
  bool isOfficeSpaceSelected = false;
  bool isCoWorkingOfficeSpaceSelected = false;

  List<SelectableItem<File>>? photos = [];
  List<SelectableItem<File>>? deprecatedPhotos = [];
  List<SelectableItem<XFile>>? brochures = [];
  List<SelectedFilePair> _selectedPickedFiles = [];

  bool get isPropertyListingEnabled => getIt<LeadratHomeBloc>().globalSettings?.shouldEnablePropertyListing ?? false;

  PropertyListingBasicInfoBloc get propertyListingBasicInfoBloc => getIt<PropertyListingBasicInfoBloc>();

  PropertyListingPropertyInfoBloc get propertyListingPropertyInfoBloc => getIt<PropertyListingPropertyInfoBloc>();

  PropertyListingFacilitiesBloc get propertyListingFacilitiesBloc => getIt<PropertyListingFacilitiesBloc>();

  PropertyListingTabBloc get propertyListingTabBloc => getIt<PropertyListingTabBloc>();

  PropertyListingGalleryBloc(this._uploadDocumentUseCase, this._addPropertyListingUseCase, this._updatePropertyUseCase, this._getGalleryDropdownUseCase) : super(const PropertyListingGalleryState()) {
    on<InitGalleryTabEvent>(_intGalleryTab);
    on<AddPhotosEvent>(_onPhotoUpload);
    on<RemovePhotosEvent>(_onRemovePhotoClicked);
    on<AddBrochuresEvent>(_onBrochureAdded);
    on<RemoveBrochuresEvent>(_onBrochureRemoved);
    on<NavigateBackToAmenitiesEvent>(_onBackButtonPressed);
    on<AddPropertyEvent>(_onAddProperty);
    on<ClearGalleryTabStateEvent>(_clearState);
    on<Add360VideoUrlEvent>(_onAdd360VideoUrl);
    on<AddThirdPartyVideoUrlEvent>(_onAddThirdPartyVideoUrl);
    on<RemoveThirdPartyVideoUrlEvent>(_onRemoveThirdPartyVideoUrl);
    on<Remove360VideoUrlEvent>(_onRemove360VideoUrl);
    on<ToggleThirdPartyEvent>(_onToggleThirdParty);
    on<Toggle360VideoUrlEvent>(_onToggle360VideoUrl);
    on<WaterMarkEvent>(_onWaterMarkEvent);
    on<UpdatePhotoOrderEvent>(_onUpdatePhotoOrder);
    on<LoadGalleryDropdownDataEvent>(_onLoadGalleryDropdownData);
    on<UpdateImageCategoryEvent>(_onUpdateImageCategory);
  }

  FutureOr<void> _intGalleryTab(InitGalleryTabEvent event, Emitter<PropertyListingGalleryState> emit) async {
    photos = [];
    brochures = [];
    _selectedPickedFiles = [];
    addPropertyListingModel = propertyListingFacilitiesBloc.addPropertyListingModel;

    // Load gallery dropdown data
    add(LoadGalleryDropdownDataEvent());
    final video360Urls = addPropertyListingModel.view360Url?.map((e) => ItemSimpleModel(title: e, value: e)).toList();
    final thirdPartyUrls = addPropertyListingModel.links?.map((e) => ItemSimpleModel(title: e, value: e)).toList();
    emit(state.copyWith(pageState: PageState.initial, photos: [], brochures: [], errorMessage: '', videos: [], degree360VideoUrls: video360Urls, thirdPartyUrls: thirdPartyUrls));

    if (addPropertyListingModel.id != null && addPropertyListingModel.id != '00000000-0000-0000-0000-000000000000') {
      addPropertyListingModel.images?.sort((a, b) {
        final rankA = a.orderRank ?? 0;
        final rankB = b.orderRank ?? 0;
        return rankA.compareTo(rankB);
      });
      if (addPropertyListingModel.images?.isNotEmpty ?? false) {
        addPropertyListingModel.images?.forEach((image) {
          if (image.galleryType == null || image.galleryType == 1) {
            photos?.add(SelectableItem<File>(title: image.name ?? '', value: File(image.imageFilePath ?? '')));
          }
        });
      }
      addPropertyListingModel.brochures?.forEach((brochure) {
        brochures?.add(SelectableItem<XFile>(title: brochure.name ?? '', value: XFile(brochure.url ?? '', name: brochure.name)));
      });
      emit(state.copyWith(pageState: PageState.initial, photos: photos, brochures: brochures, isWaterMarkEnabled: addPropertyListingModel.isWaterMarkEnabled));
    }
  }

  FutureOr<void> _onPhotoUpload(AddPhotosEvent event, Emitter<PropertyListingGalleryState> emit) async {
    if (event.isReUpload) {
      photos?.removeWhere((photo) => deprecatedPhotos?.any((deprecated) => deprecated.value?.path == photo.value?.path) ?? false);
      emit(state.copyWith(isImageQualityMatched: true));
    }
    var selectedFiles = await FilePickerUtil.pickFile(event.selectFileEnum, isMultiSelection: true);
    if (selectedFiles == null || selectedFiles.isEmpty) return;
    int unMatchImageCount = 0;
    for (var selectedFile in selectedFiles) {
      if (selectedFile?.path.isNotNullOrEmpty() ?? false) {
        var bytes = await File(selectedFile!.path).readAsBytes();

        final codec = await ui.instantiateImageCodec(bytes);
        final frame = await codec.getNextFrame();
        final image = frame.image;

        if (image.width < 1920 || image.height < 1080) {
          unMatchImageCount += 1;
          emit(state.copyWith(isImageQualityMatched: false, unMatchedImageCount: unMatchImageCount));
          deprecatedPhotos?.add(SelectableItem(title: selectedFile.path ?? "", value: File(selectedFile.path ?? ''), isEnabled: false));
        }

        photos?.add(SelectableItem(title: selectedFile.path ?? "", value: File(selectedFile.path ?? ''), isEnabled: false));
      }
    }
    emit(state.copyWith(pageState: PageState.initial, photos: photos));
  }

  Future<String?> uploadImageToS3Bucket(XFile? capturedImage, {bool isDocument = false}) async {
    if (capturedImage != null) {
      String? uploadedImage;
      final base64File = base64Encode(await capturedImage.readAsBytes());
      final uploadedImageToS3Bucket = await _uploadDocumentUseCase(UploadDocumentParams(isDocument ? BlobFolderNameEnum.propertyDocument.description : BlobFolderNameEnum.propertyImage.description, capturedImage.path.split('/').last, base64File));
      uploadedImageToS3Bucket.fold(
          (failure) => {
                // show toast
              },
          (res) => {
                uploadedImage = res?.isNotEmpty ?? false ? res : null,
              });
      return uploadedImage;
    } else {
      return null;
    }
  }

  FutureOr<void> _onRemovePhotoClicked(RemovePhotosEvent event, Emitter<PropertyListingGalleryState> emit) {
    _selectedPickedFiles.removeWhere((element) => element.compressed.path == event.photoPath);
    photos?.remove(photos?.firstWhereOrNull((photo) => photo.value?.path == event.photoPath));
    emit(state.copyWith(pageState: PageState.initial, photos: photos));
  }

  FutureOr<void> _onBackButtonPressed(NavigateBackToAmenitiesEvent event, Emitter<PropertyListingGalleryState> emit) {
    emit(state.copyWith(pageState: PageState.failure, addPropertyModel: addPropertyListingModel));
  }

  FutureOr<void> _onWaterMarkEvent(WaterMarkEvent event, Emitter<PropertyListingGalleryState> emit) {
    emit(state.copyWith(isWaterMarkEnabled: !state.isWaterMarkEnabled));
  }

  FutureOr<void> _onAddProperty(AddPropertyEvent event, Emitter<PropertyListingGalleryState> emit) async {
    emit(state.copyWith(pageState: PageState.loading, errorMessage: ''));
    List<PropertyImageModel>? uploadedImages = [];
    List<PropertyBrochureModel>? uploadedBrochures = [];

    if (photos != null && (photos?.isNotEmpty ?? false)) {
      for (var photo in photos!) {
        Map<String, int>? dimensions;
        if (photo.value?.path != null) {
          try {
            var bytes = await File(photo.value!.path).readAsBytes();
            final codec = await ui.instantiateImageCodec(bytes);
            final frame = await codec.getNextFrame();
            final image = frame.image;
            dimensions = {
              'width': image.width,
              'height': image.height,
            };
          } catch (e) {
            dimensions = null;
          }
        }

        final imagePath = photo.value?.path ?? '';
        final selectedCategory = state.imageCategories[imagePath] ?? (state.galleryDropdownItems.isNotEmpty ? state.galleryDropdownItems.first : "Images");

        if (photo.isEnabled) {
          uploadedImages.add(PropertyImageModel(
            name: photo.title,
            galleryType: 1,
            imageKey: selectedCategory,
            imageFilePath: photo.value?.path,
            isCoverImage: photos?.first.value?.path == photo.value?.path,
            height: dimensions?['height'],
            width: dimensions?['width'],
          ));
        } else {
          var uploadedUrl = await uploadImageToS3Bucket(XFile(photo.value?.path ?? ''));
          uploadedImages.add(PropertyImageModel(
            name: photo.title,
            galleryType: 1,
            imageKey: selectedCategory,
            imageFilePath: uploadedUrl,
            isCoverImage: photos?.first.value?.path == photo.value?.path,
            height: dimensions?['height'],
            width: dimensions?['width'],
          ));
        }
      }
      final orderedUploadedImages = uploadedImages.asMap().entries.map(
        (entry) {
          final index = entry.key;
          final image = entry.value;
          return image.copyWith(orderRank: index);
        },
      ).toList();

      addPropertyListingModel = addPropertyListingModel.copyWith(images: orderedUploadedImages);
    } else {
      addPropertyListingModel = addPropertyListingModel.copyWith(images: []);
    }

    if (brochures != null && (brochures?.isNotEmpty ?? false)) {
      for (var brochure in brochures!) {
        if (brochure.isEnabled) {
          uploadedBrochures.add(PropertyBrochureModel(name: brochure.title, url: brochure.value?.path));
        } else {
          var uploadedUrl = await uploadImageToS3Bucket(XFile(brochure.value?.path ?? ''));
          uploadedBrochures.add(PropertyBrochureModel(name: brochure.value?.name, url: uploadedUrl));
        }
      }
      addPropertyListingModel = addPropertyListingModel.copyWith(brochures: uploadedBrochures);
    } else {
      addPropertyListingModel = addPropertyListingModel.copyWith(brochures: []);
    }

    final basicInfoState = propertyListingBasicInfoBloc.state;
    final propertyInfoState = propertyListingPropertyInfoBloc.state;
    final selectedListingOnBehalfUser = basicInfoState.listingOnBehalfByUsers.firstWhereOrNull((element) => element.isSelected)?.value?.id;
    final monetaryInfo = addPropertyListingModel.monetaryInfo?.copyWith(
      id: addPropertyListingModel.monetaryInfo?.id ?? StringConstants.emptyGuidId,
      downpayment: double.tryParse(propertyListingPropertyInfoBloc.downPaymentController.text),
      paymentFrequency: propertyInfoState.selectablePaymentFrequencies.firstWhereOrNull((element) => element.isSelected)?.value,
      noOfChequesAllowed: propertyInfoState.numberOfCheques.firstWhereOrNull((element) => element.isSelected)?.value,
      expectedPrice: int.tryParse(propertyListingPropertyInfoBloc.totalPriceController.text),
      currency: propertyInfoState.currencies.firstWhereOrNull((element) => element.isSelected)?.title ?? "AED",
      isPriceVissible: basicInfoState.hidePriceOnApplication,
    );
    final compliance = PropertyListingCompliance(
      type: basicInfoState.uaeEmirates.firstWhereOrNull((element) => element.isSelected)?.title == "Abu Dhabi"
          ? ComplianceType.adrec
          : propertyInfoState.selectedPermitValue == "RERA"
              ? ComplianceType.rera
              : ComplianceType.dtcm,
      listingAdvertisementNumber: propertyListingPropertyInfoBloc.permitNumberController.text,
    );
    final selectedAttributes = propertyInfoState.customBasicAttributes.where((element) => element.values.any((element) => element.isSelected)).toList();
    final selectedFacilitiesAttributes = addPropertyListingModel.attributes?.nonNulls ?? [];
    List<PropertyAttributeModel>? selectedBasicAttributes = selectedAttributes.map((e) => e.customAttributesModel?.toPropertyAttributeModel(e.values.firstWhereOrNull((element) => element.isSelected)!.value!.toString())).nonNulls.toList();
    addPropertyListingModel = addPropertyListingModel.copyWith(
      attributes: [...selectedFacilitiesAttributes, ...selectedBasicAttributes],
      possesionType: propertyInfoState.selectedPossessionType,
      possessionDate: propertyInfoState.selectedPossessionType == PropertyListingPossessionType.customDate ? propertyInfoState.selectedAvailableDate.getBasedOnTimeZone() : null,
      title: propertyListingBasicInfoBloc.propertyTitleEngController.text,
      titleWithLanguage: propertyListingBasicInfoBloc.propertyTitleArbController.text,
      uaeEmirate: basicInfoState.uaeEmirates.firstWhereOrNull((element) => element.isSelected)?.value,
      completionStatus: basicInfoState.completionStatus.firstWhereOrNull((element) => element.isSelected)?.value,
      offeringType: basicInfoState.offeringTypes.firstWhereOrNull((element) => element.isSelected)?.value,
      enquiredFor: basicInfoState.enquiryTypes.firstWhereOrNull((element) => element.isSelected)?.value,
      finishingType: basicInfoState.finishingTypes.firstWhereOrNull((element) => element.isSelected)?.value,
      aboutProperty: propertyListingBasicInfoBloc.aboutPropertyEngController.text,
      aboutPropertyWithLanguage: propertyListingBasicInfoBloc.aboutPropertyArbController.text,
      furnishStatus: propertyInfoState.furnishingStatuses.firstWhereOrNull((element) => element.isSelected)?.value,
      facing: propertyInfoState.facings.firstWhereOrNull((element) => element.isSelected)?.value,
      propertyTypeId: basicInfoState.propertySubTypes.firstWhereOrNull((element) => element.isSelected)?.value,
      noOfBHK: basicInfoState.noOfBhk.firstOrNull?.value,
      address: propertyInfoState.location,
      listingOnBehalf: selectedListingOnBehalfUser != null ? [selectedListingOnBehalfUser] : null,
      assignedTo: basicInfoState.listingByUsers.where((element) => element.isSelected).map((e) => e.value?.id).nonNulls.toList(),
      monetaryInfo: monetaryInfo,
      compliance: compliance,
      dimension: PropertyDimensionModel(
        areaUnitId: basicInfoState.propertySizeUnits.firstWhereOrNull((element) => element.isSelected)?.value?.id,
        area: double.tryParse(propertyListingBasicInfoBloc.propertySizeController.text),
      ),
      notes: propertyListingPropertyInfoBloc.notesController.text,
      age: double.tryParse(propertyListingBasicInfoBloc.propertyAgeController.text),
      propertyOwnerDetails: propertyInfoState.ownerContactInformation.map((e) => e.toOwnerDetailsModel()).toList(),
    );

    if (addPropertyListingModel.id == null) {
      var response = await _addPropertyListingUseCase.call(addPropertyListingModel);
      response.fold((failure) {
        DialogManager().hideTransparentProgressDialog();
        emit(state.copyWith(pageState: PageState.loading, errorMessage: 'Some error occurred while adding the property! Please try again'));
      }, (result) {
        if (result != null) {
          getIt<AddPropertyListingBloc>().add(ChangeTabEvent(4));
          propertyListingTabBloc.add(PropertyListingInitialEvent(propertyId: result.id));
          emit(state.copyWith(pageState: PageState.success, addPropertyModel: result));
        }
        DialogManager().hideTransparentProgressDialog();
      });
    } else {
      var response = await _updatePropertyUseCase.call(addPropertyListingModel);
      response.fold((failure) {
        DialogManager().hideTransparentProgressDialog();
        LeadratCustomSnackbar.show(message: "Some error occurred while updating the property! Please try again", navigatorKey: MyApp.navigatorKey, type: SnackbarType.error);
      }, (result) {
        if (result != null) {
          getIt<AddPropertyListingBloc>().add(ChangeTabEvent(4));
          propertyListingTabBloc.add(PropertyListingInitialEvent(propertyId: result.id));
          emit(state.copyWith(pageState: PageState.success, addPropertyModel: addPropertyListingModel));
        }
        DialogManager().hideTransparentProgressDialog();
      });
    }
  }

  FutureOr<void> _onBrochureAdded(AddBrochuresEvent event, Emitter<PropertyListingGalleryState> emit) async {
    final selectedPickedFiles = await FilePickerUtil.pickFiles(event.selectFileEnum, isMultiSelection: true, existingFiles: _selectedPickedFiles?.map((e) => e.original).toList());
    selectedPickedFiles?.forEach((element) => _selectedPickedFiles.add(element));
    final selectedFiles = selectedPickedFiles?.map((e) => e.compressed);
    if (selectedFiles == null) return;
    for (var selectedFile in selectedFiles) {
      brochures?.add(SelectableItem(title: selectedFile.name ?? '', isEnabled: false, value: selectedFile));
    }
    emit(state.copyWith(pageState: PageState.initial, brochures: brochures));
  }

  FutureOr<void> _onBrochureRemoved(RemoveBrochuresEvent event, Emitter<PropertyListingGalleryState> emit) {
    brochures?.remove(brochures?.firstWhereOrNull((brochure) => brochure.value?.path == event.brochurePath));
    emit(state.copyWith(pageState: PageState.initial, brochures: brochures));
  }

  FutureOr<void> _clearState(ClearGalleryTabStateEvent event, Emitter<PropertyListingGalleryState> emit) {
    emit(state.clearState());
  }

  FutureOr<void> _onAdd360VideoUrl(Add360VideoUrlEvent event, Emitter<PropertyListingGalleryState> emit) async {
    try {
      if (event.videoUrl.isNullOrEmpty()) {
        LeadratCustomSnackbar.show(message: "Please enter a URL", navigatorKey: MyApp.navigatorKey, type: SnackbarType.error);
      } else if (await canLaunchUrl(Uri.parse(event.videoUrl))) {
        emit(state.copyWith(degree360VideoUrls: [ItemSimpleModel(title: event.videoUrl, value: event.videoUrl), ...state.degree360VideoUrls]));
        final video360Urls = (addPropertyListingModel.view360Url ?? []) + [event.videoUrl];
        addPropertyListingModel = addPropertyListingModel.copyWith(view360Url: video360Urls);
      } else {
        LeadratCustomSnackbar.show(message: "Invalid URL, please enter valid URL", navigatorKey: MyApp.navigatorKey, type: SnackbarType.error);
      }
    } catch (ex) {
      LeadratCustomSnackbar.show(message: "Invalid URL, please enter valid URL", navigatorKey: MyApp.navigatorKey, type: SnackbarType.error);
    }
  }

  FutureOr<void> _onAddThirdPartyVideoUrl(AddThirdPartyVideoUrlEvent event, Emitter<PropertyListingGalleryState> emit) async {
    try {
      if (event.thirdPartyUrl.isNullOrEmpty()) {
        LeadratCustomSnackbar.show(message: "Please enter a URL", navigatorKey: MyApp.navigatorKey, type: SnackbarType.error);
      } else if (await canLaunchUrl(Uri.parse(event.thirdPartyUrl))) {
        emit(state.copyWith(thirdPartyUrls: [ItemSimpleModel(title: event.thirdPartyUrl, value: event.thirdPartyUrl), ...state.thirdPartyUrls]));
        final links = (addPropertyListingModel.links ?? []) + [event.thirdPartyUrl];
        addPropertyListingModel = addPropertyListingModel.copyWith(links: links);
      } else {
        LeadratCustomSnackbar.show(message: "Invalid URL, please enter valid URL", navigatorKey: MyApp.navigatorKey, type: SnackbarType.error);
      }
    } catch (ex) {
      LeadratCustomSnackbar.show(message: "Invalid URL, please enter valid URL", navigatorKey: MyApp.navigatorKey, type: SnackbarType.error);
    }
  }

  FutureOr<void> _onRemoveThirdPartyVideoUrl(RemoveThirdPartyVideoUrlEvent event, Emitter<PropertyListingGalleryState> emit) {
    final updatedThirdPartyUrls = state.thirdPartyUrls.whereNot((element) => element.value == event.item.value).toList();
    emit(state.copyWith(thirdPartyUrls: updatedThirdPartyUrls));
    final updatedLinks = addPropertyListingModel.links?.whereNot((element) => element == event.item.value).toList();
    addPropertyListingModel.copyWith(links: updatedLinks);
  }

  FutureOr<void> _onRemove360VideoUrl(Remove360VideoUrlEvent event, Emitter<PropertyListingGalleryState> emit) {
    final updated360VideoUrls = state.degree360VideoUrls.whereNot((element) => element.value == event.item.value).toList();
    emit(state.copyWith(degree360VideoUrls: updated360VideoUrls));
    final video360Urls = addPropertyListingModel.view360Url?.whereNot((element) => element == event.item.value).toList();
    addPropertyListingModel.copyWith(view360Url: video360Urls);
  }

  FutureOr<void> _onToggleThirdParty(ToggleThirdPartyEvent event, Emitter<PropertyListingGalleryState> emit) async {
    try {
      if (event.item.value == null) return;
      await launchUrl(Uri.parse(event.item.value!));
    } catch (ex) {
      LeadratCustomSnackbar.show(message: "Invalid third party URL", navigatorKey: MyApp.navigatorKey, type: SnackbarType.error);
    }
  }

  FutureOr<void> _onToggle360VideoUrl(Toggle360VideoUrlEvent event, Emitter<PropertyListingGalleryState> emit) async {
    try {
      if (event.item.value == null) return;
      await launchUrl(Uri.parse(event.item.value!));
    } catch (ex) {
      LeadratCustomSnackbar.show(message: "Invalid 360 degree video URL", navigatorKey: MyApp.navigatorKey, type: SnackbarType.error);
    }
  }

  FutureOr<void> _onUpdatePhotoOrder(UpdatePhotoOrderEvent event, Emitter<PropertyListingGalleryState> emit) async {
    emit(state.copyWith(photos: event.photos));

    photos = event.photos;

    final orderedUploadedImages = await Future.wait(
      (state.photos?.asMap().entries.map((entry) async {
                final index = entry.key;
                final photo = entry.value;
                Map<String, int>? dimensions;
                if (photo.value?.path != null) {
                  try {
                    var bytes = await File(photo.value!.path).readAsBytes();
                    final codec = await ui.instantiateImageCodec(bytes);
                    final frame = await codec.getNextFrame();
                    final image = frame.image;
                    dimensions = {
                      'width': image.width,
                      'height': image.height,
                    };
                  } catch (e) {
                    dimensions = null;
                  }
                }

                final imagePath = photo.value?.path ?? '';
                final selectedCategory = state.imageCategories[imagePath] ?? (state.galleryDropdownItems.isNotEmpty ? state.galleryDropdownItems.first : "Images");

                return PropertyImageModel(name: photo.title,
                  galleryType: 1,
                  imageKey: selectedCategory,
                  imageFilePath: photo.value?.path,
                  isCoverImage: event.photos.first.value?.path == photo.value?.path,
                  orderRank: index,
                  height: dimensions?['height'],
                  width: dimensions?['width'],
                );
              }) ??
              <Future<PropertyImageModel>>[])
          .toList(),
    );

    addPropertyListingModel = addPropertyListingModel.copyWith(images: orderedUploadedImages);
  }

  /// Calculate quality score for property editing (handles both new files and existing URLs)
  Future<void> calculateQualityScore() async {
    try {
      // Separate new file uploads from existing URL-based images
      final newFiles = <File>[];
      final existingImages = <PropertyImageModel>[];

      if (photos != null) {
        for (final photo in photos!) {
          final filePath = photo.value?.path ?? '';
          if (filePath.startsWith('http')) {
            // This is a URL-based image from API
            final existingImage = addPropertyListingModel.images?.firstWhere(
              (img) => img.imageFilePath == filePath,
              orElse: () => PropertyImageModel(imageFilePath: filePath),
            );
            if (existingImage != null) {
              existingImages.add(existingImage);
            }
          } else {
            // This is a new file upload
            newFiles.add(photo.value!);
          }
        }
      }

      // Calculate quality score using unified analysis
      final score = await PropertyUtils.calculateFullScoreObject(
        propertyId: addPropertyListingModel.id ?? 'temp',
        description: addPropertyListingModel.aboutProperty,
        title: addPropertyListingModel.title,
        enquiredCity: addPropertyListingModel.address?.city,
        enquiredCommunity: addPropertyListingModel.address?.community,
        enquiredSubcommunity: addPropertyListingModel.address?.subCommunity,
        enquiredTowerName: addPropertyListingModel.address?.towerName,
        imageFiles: newFiles.isNotEmpty ? newFiles : null,
        imageModels: existingImages.isNotEmpty ? existingImages : null,
        dldPermitNumber: addPropertyListingModel.dldPermitNumber,
        dtcmPermit: addPropertyListingModel.dtcmPermit,
        isListingComplete: true, // Assume listing is complete for editing
      );

      // Emit updated state with quality score
      // Note: You'll need to add qualityScore to your state if it doesn't exist
      print('Quality Score: ${score.totalScore}/100');
      print('- Images: ${score.imagesScore}/6');
      print('- Duplicates: ${score.imageDuplicationScore}/18');
      print('- Dimensions: ${score.imageDimensionsScore}/10');
      print('- Diversity: ${score.imageDiversityScore}/5');
    } catch (e) {
      print('Error calculating quality score: $e');
    }
  }

  /// Load gallery dropdown data from API
  FutureOr<void> _onLoadGalleryDropdownData(LoadGalleryDropdownDataEvent event, Emitter<PropertyListingGalleryState> emit) async {
    try {
      final result = await _getGalleryDropdownUseCase(NoParams());
      result.fold(
        (failure) {
          print('Failed to load gallery dropdown data: ${failure.message}');
        },
        (galleryData) {
          final items = galleryData.items ?? [];
          emit(state.copyWith(galleryDropdownItems: items));
        },
      );
    } catch (e) {
      print('Error loading gallery dropdown data: $e');
    }
  }

  /// Update image category
  FutureOr<void> _onUpdateImageCategory(UpdateImageCategoryEvent event, Emitter<PropertyListingGalleryState> emit) async {
    final updatedCategories = Map<String, String>.from(state.imageCategories);
    updatedCategories[event.imagePath] = event.category;

    emit(state.copyWith(imageCategories: updatedCategories));

    // Update the photos list to reflect the new category
    if (photos != null) {
      final updatedPhotos = photos!.map((photo) {
        if (photo.value?.path == event.imagePath) {
          return SelectableItem<File>(
            title: event.category,
            value: photo.value,
            isSelected: photo.isSelected,
            isEnabled: photo.isEnabled,
          );
        }
        return photo;
      }).toList();

      photos = updatedPhotos;
      emit(state.copyWith(photos: updatedPhotos));
    }
  }
}
