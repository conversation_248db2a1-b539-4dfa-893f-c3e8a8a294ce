import 'package:leadrat/core_main/enums/property_enums/property_status.dart';
import 'package:leadrat/core_main/remote/rest_response/paged_rest_response.dart';
import 'package:leadrat/features/lead/data/models/get_lead_model.dart';
import 'package:leadrat/features/properties/data/models/gallery_dropdown_model.dart';
import 'package:leadrat/features/properties/data/models/get_property_model.dart';
import 'package:leadrat/features/properties/data/models/list_property_model.dart';
import 'package:leadrat/features/properties/data/models/properties_with_id_model.dart';
import 'package:leadrat/features/properties/data/models/property_filter_model.dart';
import 'package:leadrat/features/properties/data/models/property_lead_count_model.dart';
import 'package:leadrat/features/properties/data/models/re_assign_property_model.dart';
import 'package:leadrat/features/properties/data/models/update_property_assignedTo_model.dart';
import 'package:leadrat/features/properties/data/models/update_property_share_count_model.dart';

import '../models/add_property_model.dart';
import '../models/get_all_property_model.dart';

abstract class PropertiesRemoteDataSource {
  Future<PagedResponse<GetAllPropertyModel, String>?> getAllProperties(int pageNumber, PropertyFilterModel? propertyFilterModel);

  Future<PagedResponse<GetAllPropertyModel, String>?> searchProperties(String searchText, int pageSize, {bool isPropertyListingEnabled = false});

  Future<GetPropertyModel?> getPropertyById(String propertyId);

  Future<PagedResponse<LeadWithDegreeMatchedModel, String>?> getMatchingLeads({int? pageNumber, int? pageSize, required String propertyId,bool? isPropertyListingEnabled});

  Future<bool?> updatePropertyShareCount(UpdatePropertyShareCountModel updatePropertyShareCountModel);

  Future<List<String>?> getOwnerNames();

  Future<List<String>?> getAddresses();

  Future<List<PropertyLeadCountModel?>?> getMatchingAssociateLeadsCount(String propertyId);

  Future<List<PropertiesWithIdModel>?> getPropertyNameWithId();

  Future<bool?> reAssignProperty(ReAssignPropertyModel? reAssignModel);

  Future<bool?> updatePropertyAssignedTo(UpdatePropertyAssignedToModel? updatePropertyAssignedToModel);

  Future<AddPropertyModel?> addProperty(AddPropertyModel? addPropertyModel);

  Future<AddPropertyModel?> updateProperty(AddPropertyModel? updatePropertyModel);

  Future<String?> deleteProperty(String? propertyId);

  Future<bool?> archiveProperty(List<String?> archivePropertyList);

  Future<Map<PropertyVisibility, int>?> getPropertyListingCount({PropertyFilterModel? propertyFilterModel});

  Future<PagedResponse<GetAllPropertyModel, String>?> getAllPropertyListing(int pageNumber, PropertyFilterModel? propertyFilterModel, [int pageSize = 10]);

  Future<bool?> listProperties(ListPropertyModel models);

  Future<bool?> deListProperties(ListPropertyModel models);

  Future<List<String>?> getCurrencies();

  Future<GalleryDropdownModel> getGalleryDropdownData();

}
